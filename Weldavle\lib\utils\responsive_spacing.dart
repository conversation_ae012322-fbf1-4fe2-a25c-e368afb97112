import 'package:flutter/widgets.dart';

/// Small responsive spacing helpers used across the app.
/// Keeps the same visual proportions while increasing paddings/gaps on larger screens.
class RS {
  static double _w(BuildContext c) => MediaQuery.of(c).size.width;

  // Horizontal padding of main content
  static double horizontalPadding(BuildContext c) {
    final w = _w(c);
    if (w < 360) return 12;
    if (w < 600) return 16;
    if (w < 1000) return 24;
    return 32;
  }

  // Inner horizontal padding for cards/rows
  static double innerPadding(BuildContext c) => horizontalPadding(c) * 0.9;

  // Vertical gaps
  static double vGapSmall(BuildContext c) {
    final w = _w(c);
    if (w < 360) return 6;
    if (w < 600) return 8;
    if (w < 1000) return 12;
    return 16;
  }

  static double vGapMedium(BuildContext c) {
    final w = _w(c);
    if (w < 360) return 12;
    if (w < 600) return 16;
    if (w < 1000) return 20;
    return 28;
  }

  static double vGapLarge(BuildContext c) {
    final w = _w(c);
    if (w < 360) return 20;
    if (w < 600) return 28;
    if (w < 1000) return 36;
    return 48;
  }

  // Horizontal gaps
  static double hGapSmall(BuildContext c) => vGapSmall(c);
  static double hGapMedium(BuildContext c) => vGapMedium(c);

  // Top header offset (space below top controls before main content)
  static double topHeader(BuildContext c) {
    final w = _w(c);
    if (w < 360) return 64;
    if (w < 600) return 80;
    if (w < 1000) return 96;
    return 120;
  }

  // Standard card inner padding
  static double cardPadding(BuildContext c) {
    final w = _w(c);
    if (w < 360) return 8;
    if (w < 600) return 10;
    return 12;
  }

  // Menu button height (primary big buttons on home)
  static double menuButtonHeight(BuildContext c) {
    final w = _w(c);
    if (w < 360) return 64;
    if (w < 600) return 72;
    if (w < 1000) return 80;
    return 96;
  }
}
