import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_provider.dart';
import '../models/question.dart';
import 'question_screen.dart';

class MockTestScreen extends StatefulWidget {
  const MockTestScreen({super.key});

  @override
  State<MockTestScreen> createState() => _MockTestScreenState();
}

class _MockTestScreenState extends State<MockTestScreen> {
  int _selectedQuestionCount = 10;
  int _selectedTimeLimit = 15; // minutes

  final List<int> _questionCounts = [5, 10, 15, 20, 25];
  final List<int> _timeLimits = [10, 15, 20, 30, 45]; // minutes

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Mock Test'),
      ),
      body: Consumer<AppProvider>(
        builder: (context, appProvider, child) {
          final totalQuestions = appProvider.allQuestions.length;
          
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Mock Test Setup',
                          style: Theme.of(context).textTheme.headlineSmall,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Configure your mock test settings and start practicing under exam conditions.',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 20),

                // Question Count Selection
                Text(
                  'Number of Questions',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 12),
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        Text(
                          'Available: $totalQuestions questions',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                        const SizedBox(height: 12),
                        Wrap(
                          spacing: 8,
                          children: _questionCounts
                              .where((count) => count <= totalQuestions)
                              .map((count) => ChoiceChip(
                                    label: Text('$count'),
                                    selected: _selectedQuestionCount == count,
                                    onSelected: (selected) {
                                      if (selected) {
                                        setState(() => _selectedQuestionCount = count);
                                      }
                                    },
                                  ))
                              .toList(),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 20),

                // Time Limit Selection
                Text(
                  'Time Limit',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 12),
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        Text(
                          'Recommended: ${(_selectedQuestionCount * 1.5).round()} minutes',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                        const SizedBox(height: 12),
                        Wrap(
                          spacing: 8,
                          children: _timeLimits
                              .map((time) => ChoiceChip(
                                    label: Text('${time}m'),
                                    selected: _selectedTimeLimit == time,
                                    onSelected: (selected) {
                                      if (selected) {
                                        setState(() => _selectedTimeLimit = time);
                                      }
                                    },
                                  ))
                              .toList(),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 20),

                // Test Summary
                Card(
                  color: Theme.of(context).colorScheme.primaryContainer.withOpacity(0.3),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Test Summary',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text('Questions:'),
                            Text('$_selectedQuestionCount'),
                          ],
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text('Time Limit:'),
                            Text('$_selectedTimeLimit minutes'),
                          ],
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text('Time per Question:'),
                            Text('${(_selectedTimeLimit * 60 / _selectedQuestionCount).round()} seconds'),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 30),

                // Start Test Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: totalQuestions >= _selectedQuestionCount
                        ? () => _startMockTest(context, appProvider)
                        : null,
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: const Text(
                      'Start Mock Test',
                      style: TextStyle(fontSize: 18),
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Instructions
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Instructions',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        const Text('• Answer all questions within the time limit'),
                        const Text('• You can review and change answers before submitting'),
                        const Text('• The test will auto-submit when time expires'),
                        const Text('• Your results will be saved for future reference'),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  void _startMockTest(BuildContext context, AppProvider appProvider) {
    // Show confirmation dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Start Mock Test'),
        content: Text(
          'Are you ready to start the mock test?\n\n'
          'Questions: $_selectedQuestionCount\n'
          'Time Limit: $_selectedTimeLimit minutes\n\n'
          'The timer will start immediately after you confirm.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _navigateToTest(context, appProvider);
            },
            child: const Text('Start Test'),
          ),
        ],
      ),
    );
  }

  void _navigateToTest(BuildContext context, AppProvider appProvider) {
    final questions = appProvider.getRandomQuestions(_selectedQuestionCount);
    
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MockTestQuestionScreen(
          questions: questions,
          timeLimit: _selectedTimeLimit,
        ),
      ),
    ).then((result) {
      if (result != null && result is TestResult) {
        _showTestResults(context, result);
      }
    });
  }

  void _showTestResults(BuildContext context, TestResult result) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Test Completed!'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Score: ${result.correctAnswers}/${result.totalQuestions}'),
            Text('Percentage: ${result.percentage.toStringAsFixed(1)}%'),
            Text('Time: ${(result.timeSpentSeconds / 60).toStringAsFixed(1)} minutes'),
            const SizedBox(height: 16),
            Text(
              result.percentage >= 70 ? 'Great job! 🎉' : 'Keep practicing! 💪',
              style: Theme.of(context).textTheme.titleMedium,
            ),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}

class MockTestQuestionScreen extends StatelessWidget {
  final List<Question> questions;
  final int timeLimit;

  const MockTestQuestionScreen({
    super.key,
    required this.questions,
    required this.timeLimit,
  });

  @override
  Widget build(BuildContext context) {
    return QuestionScreen(
      questions: questions,
      currentIndex: 0,
      mode: QuestionMode.mockTest,
      timeLimit: timeLimit,
    );
  }
}
