import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_provider.dart';
import '../models/question.dart';
import 'question_screen.dart';

class WrongAnswersScreen extends StatelessWidget {
  const WrongAnswersScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Wrong Answers'),
        actions: [
          Consumer<AppProvider>(
            builder: (context, appProvider, child) {
              final wrongQuestions = appProvider.wrongAnswerQuestions;
              if (wrongQuestions.isEmpty) return const SizedBox.shrink();
              
              return PopupMenuButton(
                itemBuilder: (context) => [
                  PopupMenuItem(
                    child: const Text('Clear All'),
                    onTap: () => _clearAllWrongAnswers(context, appProvider),
                  ),
                ],
              );
            },
          ),
        ],
      ),
      body: Consumer<AppProvider>(
        builder: (context, appProvider, child) {
          final wrongQuestions = appProvider.wrongAnswerQuestions;

          if (wrongQuestions.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.check_circle_outline,
                    size: 64,
                    color: Theme.of(context).colorScheme.outline,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No wrong answers yet',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Questions you answer incorrectly will appear here for review',
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              // Header with count and actions
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        '${wrongQuestions.length} questions to review',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                    ),
                    ElevatedButton.icon(
                      onPressed: () => _startWrongAnswersPractice(context, wrongQuestions),
                      icon: const Icon(Icons.refresh),
                      label: const Text('Practice All'),
                    ),
                  ],
                ),
              ),

              // Questions List
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: wrongQuestions.length,
                  itemBuilder: (context, index) {
                    final question = wrongQuestions[index];
                    return _buildQuestionCard(context, question, appProvider);
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildQuestionCard(BuildContext context, Question question, AppProvider appProvider) {
    final isBookmarked = appProvider.bookmarks.contains(question.id);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        title: Text(
          question.question,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Row(
              children: [
                Chip(
                  label: Text(
                    question.category,
                    style: const TextStyle(fontSize: 12),
                  ),
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
                const SizedBox(width: 8),
                Icon(
                  Icons.error_outline,
                  size: 16,
                  color: Theme.of(context).colorScheme.error,
                ),
              ],
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: Icon(
                isBookmarked ? Icons.bookmark : Icons.bookmark_border,
                color: isBookmarked ? Theme.of(context).colorScheme.primary : null,
              ),
              onPressed: () => appProvider.toggleBookmark(question.id),
            ),
            IconButton(
              icon: const Icon(Icons.check),
              color: Colors.green,
              onPressed: () => _markAsCorrect(context, appProvider, question.id),
            ),
            const Icon(Icons.arrow_forward_ios),
          ],
        ),
        onTap: () => Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => QuestionScreen(
              questions: [question],
              currentIndex: 0,
              mode: QuestionMode.practice,
            ),
          ),
        ),
      ),
    );
  }

  void _startWrongAnswersPractice(BuildContext context, List<Question> questions) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => QuestionScreen(
          questions: questions,
          currentIndex: 0,
          mode: QuestionMode.practice,
        ),
      ),
    );
  }

  void _markAsCorrect(BuildContext context, AppProvider appProvider, String questionId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Mark as Correct'),
        content: const Text('Remove this question from wrong answers list?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              appProvider.removeWrongAnswer(questionId);
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Question removed from wrong answers'),
                ),
              );
            },
            child: const Text('Remove'),
          ),
        ],
      ),
    );
  }

  void _clearAllWrongAnswers(BuildContext context, AppProvider appProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Wrong Answers'),
        content: const Text('Are you sure you want to clear all wrong answers? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final wrongAnswers = List<String>.from(appProvider.wrongAnswers);
              for (final questionId in wrongAnswers) {
                await appProvider.removeWrongAnswer(questionId);
              }
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('All wrong answers cleared'),
                ),
              );
            },
            child: const Text('Clear All'),
          ),
        ],
      ),
    );
  }
}
