import 'package:flutter/material.dart';

extension ColorExtension on Color {
  /// Creates a copy of this color with the given values replaced.
  ///
  /// All values are optional and will use the current color's values if not provided.
  Color withValues({int? red, int? green, int? blue, double? alpha}) {
    return Color.fromRGBO(
      red ?? ((r * 255.0).round() & 0xff),
      green ?? ((g * 255.0).round() & 0xff),
      blue ?? ((b * 255.0).round() & 0xff),
      alpha ?? a,
    );
  }
}
