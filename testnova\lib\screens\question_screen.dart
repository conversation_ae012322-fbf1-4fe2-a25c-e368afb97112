import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_provider.dart';
import '../models/question.dart';
import 'package:uuid/uuid.dart';

enum QuestionMode { practice, mockTest }

class QuestionScreen extends StatefulWidget {
  final List<Question> questions;
  final int currentIndex;
  final QuestionMode mode;
  final int? timeLimit; // in minutes for mock tests

  const QuestionScreen({
    super.key,
    required this.questions,
    required this.currentIndex,
    required this.mode,
    this.timeLimit,
  });

  @override
  State<QuestionScreen> createState() => _QuestionScreenState();
}

class _QuestionScreenState extends State<QuestionScreen> {
  late PageController _pageController;
  int _currentIndex = 0;
  final Map<int, int> _selectedAnswers = {};
  bool _showExplanation = false;
  DateTime? _startTime;
  DateTime? _endTime;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.currentIndex;
    _pageController = PageController(initialPage: _currentIndex);
    _startTime = DateTime.now();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final question = widget.questions[_currentIndex];
    final selectedAnswer = _selectedAnswers[_currentIndex];
    final isAnswered = selectedAnswer != null;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Question ${_currentIndex + 1} of ${widget.questions.length}',
        ),
        actions: [
          Consumer<AppProvider>(
            builder: (context, appProvider, child) {
              final isBookmarked = appProvider.bookmarks.contains(question.id);
              return IconButton(
                icon: Icon(
                  isBookmarked ? Icons.bookmark : Icons.bookmark_border,
                ),
                onPressed: () => appProvider.toggleBookmark(question.id),
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Progress Bar
          LinearProgressIndicator(
            value: (_currentIndex + 1) / widget.questions.length,
          ),

          Expanded(
            child: PageView.builder(
              controller: _pageController,
              itemCount: widget.questions.length,
              onPageChanged: (index) {
                setState(() {
                  _currentIndex = index;
                  _showExplanation = false;
                });
              },
              itemBuilder: (context, index) {
                return _buildQuestionPage(widget.questions[index], index);
              },
            ),
          ),

          // Navigation and Action Buttons
          _buildBottomActions(),
        ],
      ),
    );
  }

  Widget _buildQuestionPage(Question question, int index) {
    final selectedAnswer = _selectedAnswers[index];
    final isAnswered = selectedAnswer != null;

    return LayoutBuilder(
      builder: (context, constraints) {
        final isWideScreen = constraints.maxWidth > 800;
        final padding = isWideScreen ? 32.0 : 16.0;

        return SingleChildScrollView(
          padding: EdgeInsets.all(padding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Question
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        question.question,
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 8),
                      Chip(
                        label: Text(question.category),
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Options
              ...question.options.asMap().entries.map((entry) {
                final optionIndex = entry.key;
                final option = entry.value;
                final isSelected = selectedAnswer == optionIndex;
                final isCorrect = optionIndex == question.correctAnswer;

                Color? cardColor;
                if (isAnswered && _showExplanation) {
                  if (isCorrect) {
                    cardColor = Colors.green.withOpacity(0.1);
                  } else if (isSelected && !isCorrect) {
                    cardColor = Colors.red.withOpacity(0.1);
                  }
                }

                return Card(
                  color: cardColor,
                  margin: const EdgeInsets.only(bottom: 8),
                  child: RadioListTile<int>(
                    title: Text(option),
                    value: optionIndex,
                    groupValue: selectedAnswer,
                    onChanged: _showExplanation
                        ? null
                        : (value) {
                            setState(() {
                              _selectedAnswers[index] = value!;
                            });
                          },
                    activeColor: Theme.of(context).colorScheme.primary,
                  ),
                );
              }),

              const SizedBox(height: 16),

              // Explanation
              if (_showExplanation && isAnswered)
                Card(
                  color: Theme.of(
                    context,
                  ).colorScheme.primaryContainer.withOpacity(0.3),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              selectedAnswer == question.correctAnswer
                                  ? Icons.check_circle
                                  : Icons.cancel,
                              color: selectedAnswer == question.correctAnswer
                                  ? Colors.green
                                  : Colors.red,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              selectedAnswer == question.correctAnswer
                                  ? 'Correct!'
                                  : 'Incorrect',
                              style: Theme.of(context).textTheme.titleMedium
                                  ?.copyWith(
                                    color:
                                        selectedAnswer == question.correctAnswer
                                        ? Colors.green
                                        : Colors.red,
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Explanation:',
                          style: Theme.of(context).textTheme.titleSmall
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 4),
                        Text(question.explanation),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildBottomActions() {
    final selectedAnswer = _selectedAnswers[_currentIndex];
    final isAnswered = selectedAnswer != null;
    final isLastQuestion = _currentIndex == widget.questions.length - 1;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // Previous Button
          if (_currentIndex > 0)
            Expanded(
              child: OutlinedButton(
                onPressed: () {
                  _pageController.previousPage(
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                  );
                },
                child: const Text('Previous'),
              ),
            ),

          if (_currentIndex > 0) const SizedBox(width: 12),

          // Check Answer / Next Button
          Expanded(
            flex: 2,
            child: ElevatedButton(
              onPressed: isAnswered ? () => _handleNext() : null,
              child: Text(
                !_showExplanation && isAnswered
                    ? 'Check Answer'
                    : isLastQuestion
                    ? 'Finish'
                    : 'Next',
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _handleNext() {
    if (!_showExplanation) {
      setState(() {
        _showExplanation = true;
      });

      // Track wrong answer
      final question = widget.questions[_currentIndex];
      final selectedAnswer = _selectedAnswers[_currentIndex]!;
      if (selectedAnswer != question.correctAnswer) {
        context.read<AppProvider>().addWrongAnswer(question.id);
      }
    } else {
      if (_currentIndex == widget.questions.length - 1) {
        _finishSession();
      } else {
        _pageController.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    }
  }

  void _finishSession() {
    _endTime = DateTime.now();
    final timeSpent = _endTime!.difference(_startTime!).inSeconds;

    // Calculate results
    int correctAnswers = 0;
    List<UserAnswer> userAnswers = [];

    for (int i = 0; i < widget.questions.length; i++) {
      final question = widget.questions[i];
      final selectedAnswer = _selectedAnswers[i];

      if (selectedAnswer != null) {
        final isCorrect = selectedAnswer == question.correctAnswer;
        if (isCorrect) correctAnswers++;

        userAnswers.add(
          UserAnswer(
            questionId: question.id,
            selectedAnswer: selectedAnswer,
            isCorrect: isCorrect,
            timestamp: DateTime.now(),
          ),
        );
      }
    }

    // Save test result
    final testResult = TestResult(
      id: const Uuid().v4(),
      testType: widget.mode == QuestionMode.practice ? 'practice' : 'mock',
      totalQuestions: widget.questions.length,
      correctAnswers: correctAnswers,
      timeSpentSeconds: timeSpent,
      completedAt: _endTime!,
      answers: userAnswers,
    );

    context.read<AppProvider>().saveTestResult(testResult);

    // Navigate back with results
    Navigator.pop(context, testResult);
  }
}
