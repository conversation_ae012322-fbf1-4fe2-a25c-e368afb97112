import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_provider.dart';
import 'practice_screen.dart';
import 'mock_test_screen.dart';
import 'wrong_answers_screen.dart';
import 'settings_screen.dart';
import '../widgets/animated_background.dart';
import '../utils/responsive_spacing.dart';
import 'bookmarks_screen.dart';
import 'stats_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentIndex = 0;

  final List<Widget> _screens = [
    const HomeTab(),
    const PracticeScreen(),
    const BookmarksScreen(),
    const StatsScreen(),
    const SettingsScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, appProvider, _) {
        if (appProvider.isLoading) {
          return const LoadingScreen();
        }

        return BackgroundScaffold(
          body: _screens[_currentIndex],
          backgroundStyle: appProvider.backgroundStyle,
          backgroundColorHex: appProvider.backgroundColorHex,
        );
      },
    );
  }
}

// ColorExtension moved to lib/extensions/color_extension.dart

class HomeTab extends StatelessWidget {
  const HomeTab({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, appProvider, child) {
        return Stack(
          children: [
            // Main content: centered and simplified
            Align(
              alignment: Alignment.topCenter,
              child: SingleChildScrollView(
                padding: EdgeInsets.only(top: RS.topHeader(context)),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SizedBox(height: RS.vGapSmall(context)),
                    Text(
                      'Weldable',
                      style: Theme.of(context).textTheme.displayLarge?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w900,
                      ),
                    ),
                    SizedBox(height: RS.vGapSmall(context)),
                    Text(
                      'Master Welding — Learn Fast',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Colors.white.withOpacity(0.9),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: RS.vGapLarge(context)),

                    // large gradient menu cards
                    Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: RS.horizontalPadding(context),
                      ),
                      child: Column(
                        children: [
                          _buildBigCard(
                            context,
                            'Practice Mode',
                            Icons.school,
                            () => Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (_) => const PracticeScreen(),
                              ),
                            ),
                          ),
                          SizedBox(height: RS.vGapMedium(context)),
                          _buildBigCard(
                            context,
                            'Exam',
                            Icons.assessment,
                            () => Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (_) => const MockTestScreen(),
                              ),
                            ),
                          ),
                          SizedBox(height: RS.vGapMedium(context)),
                          _buildBigCard(
                            context,
                            'Wrong Answers',
                            Icons.report_problem,
                            () => Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (_) => const WrongAnswersScreen(),
                              ),
                            ),
                          ),
                          SizedBox(height: RS.vGapMedium(context)),
                          _buildBigCard(
                            context,
                            'Settings',
                            Icons.settings,
                            () => Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (_) => const SettingsScreen(),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: RS.vGapLarge(context)),
                  ],
                ),
              ),
            ),

            // Top-left sound and music icons
            Positioned(
              top: 16,
              left: 16,
              child: Row(
                children: [
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(24),
                    ),
                    child: IconButton(
                      icon: Icon(
                        appProvider.isSoundEnabled
                            ? Icons.volume_up
                            : Icons.volume_off,
                        color: Colors.white,
                      ),
                      onPressed: () {
                        appProvider.toggleSound();
                      },
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(24),
                    ),
                    child: IconButton(
                      icon: Icon(
                        appProvider.isMusicEnabled
                            ? Icons.music_note
                            : Icons.music_off,
                        color: Colors.white,
                      ),
                      onPressed: () {
                        appProvider.toggleMusic();
                      },
                    ),
                  ),
                  const SizedBox(width: 8),
                  // Settings button beside music
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(24),
                    ),
                    child: IconButton(
                      icon: const Icon(Icons.settings, color: Colors.white),
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (_) => const SettingsScreen(),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
            // Top-right theme picker
            Positioned(
              top: 16,
              right: 16,
              child: Consumer<AppProvider>(
                builder: (context, app, _) {
                  String label = 'System';
                  if (app.themePreference == 'light') label = 'Light';
                  if (app.themePreference == 'dark') label = 'Dark';

                  return Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.12),
                      borderRadius: BorderRadius.circular(24),
                    ),
                    child: PopupMenuButton<String>(
                      tooltip: 'Theme',
                      onSelected: (value) {
                        app.setThemePreference(value);
                      },
                      itemBuilder: (_) => [
                        const PopupMenuItem(
                          value: 'system',
                          child: Text('System'),
                        ),
                        const PopupMenuItem(
                          value: 'light',
                          child: Text('Light'),
                        ),
                        const PopupMenuItem(value: 'dark', child: Text('Dark')),
                      ],
                      child: Center(
                        child: Text(
                          label.substring(0, 1),
                          style: const TextStyle(color: Colors.white),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  // ...existing code...

  Widget _buildBigCard(
    BuildContext context,
    String label,
    IconData icon,
    VoidCallback onTap,
  ) {
    final theme = Theme.of(context);
    return Material(
      borderRadius: BorderRadius.circular(20),
      elevation: 8,
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(20),
        child: Container(
          height: RS.menuButtonHeight(context) + 8,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: LinearGradient(
              colors: [theme.colorScheme.primary, theme.colorScheme.secondary],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            boxShadow: [
              BoxShadow(
                color: theme.colorScheme.primary.withOpacity(0.22),
                blurRadius: 18,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: RS.innerPadding(context)),
            child: Row(
              children: [
                Icon(icon, color: Colors.white, size: 28),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    label,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.white,
                  size: 18,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class LoadingScreen extends StatelessWidget {
  const LoadingScreen({super.key});
  @override
  Widget build(BuildContext context) =>
      const Scaffold(body: Center(child: CircularProgressIndicator()));
}
