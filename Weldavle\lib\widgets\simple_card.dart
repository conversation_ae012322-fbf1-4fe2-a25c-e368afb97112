import 'package:flutter/material.dart';
import '../utils/responsive_spacing.dart';

/// A lightweight, consistent card used across the app for a simple modern look.
class SimpleCard extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final BorderRadius? borderRadius;
  final Gradient? gradient;
  final Color? color;
  final double elevation;

  const SimpleCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.borderRadius,
    this.gradient,
    this.color,
    this.elevation = 8.0,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final radius = borderRadius ?? BorderRadius.circular(14);

    return Container(
      margin: margin,
      child: Material(
        color: Colors.transparent,
        elevation: elevation,
        borderRadius: radius,
        shadowColor: theme.colorScheme.primary.withOpacity(0.08),
        child: Container(
          decoration: BoxDecoration(
            gradient: gradient,
            color: gradient == null ? (color ?? theme.cardColor) : null,
            borderRadius: radius,
          ),
          padding: padding ?? EdgeInsets.all(RS.cardPadding(context)),
          child: child,
        ),
      ),
    );
  }
}
