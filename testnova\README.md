# Testnova - Welding MCQ Practice App

A comprehensive Flutter application for practicing welding multiple-choice questions, designed for offline use with a clean, responsive interface.

## Features

### 🎯 Core Functionality
- **Practice Questions**: Study individual questions at your own pace
- **Mock Tests**: Timed tests to simulate exam conditions
- **Bookmarks**: Save important questions for later review
- **Wrong Answers**: Track and review incorrectly answered questions
- **Statistics**: Monitor your progress and performance

### 📱 User Experience
- **Offline First**: All data stored locally, no internet required
- **Responsive Design**: Optimized for mobile, tablet, and desktop
- **Dark/Light Theme**: Toggle between themes for comfortable studying
- **Progress Tracking**: Visual progress indicators and statistics
- **Clean UI**: Minimalistic design with soft color palettes

### 📚 Content
- **10+ Welding Questions**: Covering various welding topics including:
  - Heat Treatment (post-heating, preheating)
  - Cutting Processes (oxy-fuel cutting)
  - TIG Welding (polarity, aluminum welding)
  - Metallurgy (CEV, carbon steel)
  - MIG Welding (shielding gases)
  - Electrodes (AWS classifications)
  - NDT Methods (surface crack detection)
  - Defects (hydrogen cracking)
  - Standards (AWS D1.1)

## Getting Started

### Prerequisites
- Flutter SDK (3.8.1 or higher)
- Dart SDK
- Any IDE with Flutter support (VS Code, Android Studio, etc.)

### Installation
1. Clone the repository
2. Navigate to the project directory
3. Install dependencies:
   ```bash
   flutter pub get
   ```
4. Run the app:
   ```bash
   flutter run
   ```

### Building for Production
- **Web**: `flutter build web`
- **Android**: `flutter build apk`
- **iOS**: `flutter build ios`
- **Windows**: `flutter build windows`
- **macOS**: `flutter build macos`
- **Linux**: `flutter build linux`

## Usage

### Practice Mode
1. Navigate to the Practice tab
2. Filter questions by category (optional)
3. Select individual questions or use "Random Practice"
4. Answer questions and view explanations
5. Bookmark important questions

### Mock Test Mode
1. Go to the Mock Test section
2. Configure test settings:
   - Number of questions (5-25)
   - Time limit (10-45 minutes)
3. Start the timed test
4. Review results and performance

### Progress Tracking
- View overall statistics in the Stats tab
- Monitor study streak and average scores
- Review recent test results
- Track bookmarked and wrong answer counts

## Technical Architecture

### 🏗️ Structure
```
lib/
├── models/           # Data models (Question, UserAnswer, TestResult)
├── services/         # Data service for local storage
├── providers/        # State management with Provider
├── screens/          # UI screens
│   ├── home_screen.dart
│   ├── practice_screen.dart
│   ├── question_screen.dart
│   ├── mock_test_screen.dart
│   ├── bookmarks_screen.dart
│   ├── wrong_answers_screen.dart
│   └── stats_screen.dart
└── main.dart         # App entry point
```

### 🔧 Dependencies
- **provider**: State management
- **shared_preferences**: Local data persistence
- **sqflite**: Local database (future expansion)
- **uuid**: Unique ID generation

### 💾 Data Storage
- Questions stored in SharedPreferences as JSON
- User progress and statistics tracked locally
- Bookmarks and wrong answers persisted offline
- Test results saved for historical analysis

## Customization

### Adding More Questions
Edit `lib/services/data_service.dart` and add questions to the `_getDefaultWeldingQuestions()` method:

```dart
Question(
  id: 'unique_id',
  question: 'Your question text',
  options: ['Option A', 'Option B', 'Option C', 'Option D'],
  correctAnswer: 0, // Index of correct answer (0-3)
  explanation: 'Detailed explanation',
  category: 'Category Name',
),
```

## Performance Features

- **Lazy Loading**: Questions loaded efficiently
- **Responsive Layout**: Adapts to different screen sizes
- **Smooth Animations**: Micro-interactions for better UX
- **Memory Efficient**: Optimized for mobile devices

## Future Enhancements

- [ ] Import/Export question banks
- [ ] Cloud synchronization (optional)
- [ ] Advanced analytics and insights
- [ ] Question difficulty levels
- [ ] Study reminders and notifications
- [ ] Multiple question formats (true/false, fill-in-the-blank)

---

**Testnova** - Master welding concepts through practice and repetition. 🔥
