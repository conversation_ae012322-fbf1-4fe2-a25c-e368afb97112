import 'package:flutter/material.dart';
import '../models/question.dart';
import '../services/data_service.dart';

class AppProvider extends ChangeNotifier {
  final DataService _dataService = DataService.instance;

  List<Question> _allQuestions = [];
  Set<String> _bookmarks = {};
  Set<String> _wrongAnswers = {};
  List<TestResult> _testResults = [];
  Map<String, dynamic> _userStats = {};
  bool _isDarkMode = false;
  // 'system', 'light', 'dark'
  String _themePreference = 'system';
  String _colorPaletteId = 'royal';
  String _backgroundStyle = 'pattern'; // 'pattern'|'gradient'|'solid'
  String _backgroundColorHex = '#0F1226';
  bool _isLoading = true;
  bool _isSoundEnabled = true;
  bool _isMusicEnabled = true;

  // Getters
  List<Question> get allQuestions => _allQuestions;
  Set<String> get bookmarks => _bookmarks;
  Set<String> get wrongAnswers => _wrongAnswers;
  List<TestResult> get testResults => _testResults;
  Map<String, dynamic> get userStats => _userStats;
  bool get isDarkMode => _isDarkMode;
  String get themePreference => _themePreference;
  String get colorPaletteId => _colorPaletteId;
  String get backgroundColorHex => _backgroundColorHex;
  bool get isLoading => _isLoading;
  bool get isSoundEnabled => _isSoundEnabled;
  bool get isMusicEnabled => _isMusicEnabled;
  String get backgroundStyle => _backgroundStyle;

  List<Question> get bookmarkedQuestions =>
      _allQuestions.where((q) => _bookmarks.contains(q.id)).toList();

  List<Question> get wrongAnswerQuestions =>
      _allQuestions.where((q) => _wrongAnswers.contains(q.id)).toList();

  Future<void> initialize() async {
    _isLoading = true;
    notifyListeners();

    try {
      await _dataService.init();
      await _loadData();
      await _loadThemePreference();
      await _loadColorPalette();
      await _loadBackgroundStyle();
      await _loadBackgroundColor();
    } catch (e) {
      debugPrint('Error initializing app: $e');
    }

    _isLoading = false;
    notifyListeners();
  }

  Future<void> _loadColorPalette() async {
    final id = await _dataService.getColorPalette();
    _colorPaletteId = id ?? 'royal';
    notifyListeners();
  }

  Future<void> _loadBackgroundStyle() async {
    final id = await _dataService.getBackground();
    _backgroundStyle = id ?? 'pattern';
    notifyListeners();
  }

  Future<void> _loadBackgroundColor() async {
    final hex = await _dataService.getBackgroundColor();
    _backgroundColorHex = hex ?? '#0F1226';
    notifyListeners();
  }

  Future<void> _loadThemePreference() async {
    final pref = await _dataService.getThemePreference();
    _themePreference = pref ?? 'system';
    // determine initial isDarkMode based on preference and system
    if (_themePreference == 'system') {
      // default to system dark when unknown; callers should call updateSystemBrightness
      _isDarkMode =
          WidgetsBinding.instance.window.platformBrightness == Brightness.dark;
    } else if (_themePreference == 'dark') {
      _isDarkMode = true;
    } else {
      _isDarkMode = false;
    }
    notifyListeners();
  }

  Future<void> _loadData() async {
    _allQuestions = await _dataService.getQuestions();
    _bookmarks = await _dataService.getBookmarks();
    _wrongAnswers = await _dataService.getWrongAnswers();
    _testResults = await _dataService.getTestResults();
    _userStats = await _dataService.getUserStats();
  }

  Future<void> toggleBookmark(String questionId) async {
    await _dataService.toggleBookmark(questionId);
    _bookmarks = await _dataService.getBookmarks();
    notifyListeners();
  }

  Future<void> addWrongAnswer(String questionId) async {
    await _dataService.addWrongAnswer(questionId);
    _wrongAnswers = await _dataService.getWrongAnswers();
    notifyListeners();
  }

  Future<void> removeWrongAnswer(String questionId) async {
    await _dataService.removeWrongAnswer(questionId);
    _wrongAnswers = await _dataService.getWrongAnswers();
    notifyListeners();
  }

  Future<void> clearWrongAnswers() async {
    await _dataService.clearWrongAnswers();
    _wrongAnswers = await _dataService.getWrongAnswers();
    notifyListeners();
  }

  Future<void> saveTestResult(TestResult result) async {
    await _dataService.saveTestResult(result);
    _testResults = await _dataService.getTestResults();
    await _updateUserStats(result);
    notifyListeners();
  }

  Future<void> clearTestResults() async {
    await _dataService.clearTestResults();
    _testResults = await _dataService.getTestResults();
    notifyListeners();
  }

  Future<void> _updateUserStats(TestResult result) async {
    final stats = Map<String, dynamic>.from(_userStats);

    stats['totalQuestions'] =
        (stats['totalQuestions'] ?? 0) + result.totalQuestions;
    stats['correctAnswers'] =
        (stats['correctAnswers'] ?? 0) + result.correctAnswers;
    stats['testsCompleted'] = (stats['testsCompleted'] ?? 0) + 1;

    final totalCorrect = stats['correctAnswers'];
    final totalQuestions = stats['totalQuestions'];

    stats['averageScore'] = totalQuestions > 0
        ? (totalCorrect / totalQuestions) * 100
        : 0.0;
    stats['lastStudyDate'] = DateTime.now().toIso8601String();

    // Update streak
    final lastDate = stats['lastStudyDate'];
    if (lastDate != null) {
      final last = DateTime.parse(lastDate);
      final now = DateTime.now();
      final difference = now.difference(last).inDays;

      if (difference <= 1) {
        stats['streak'] = (stats['streak'] ?? 0) + 1;
      } else {
        stats['streak'] = 1;
      }
    } else {
      stats['streak'] = 1;
    }

    _userStats = stats;
    await _dataService.updateUserStats(stats);
  }

  Future<void> resetUserStats() async {
    await _dataService.resetUserStats();
    _userStats = await _dataService.getUserStats();
    notifyListeners();
  }

  void toggleTheme() {
    // cycle light -> dark -> system -> light
    if (_themePreference == 'light') {
      setThemePreference('dark');
    } else if (_themePreference == 'dark') {
      setThemePreference('system');
    } else {
      setThemePreference('light');
    }
    notifyListeners();
  }

  Future<void> setThemePreference(String pref) async {
    _themePreference = pref; // 'system'|'light'|'dark'
    await _dataService.setThemePreference(pref);
    if (pref == 'system') {
      _isDarkMode =
          WidgetsBinding.instance.window.platformBrightness == Brightness.dark;
    } else if (pref == 'dark') {
      _isDarkMode = true;
    } else {
      _isDarkMode = false;
    }
    notifyListeners();
  }

  Future<void> setColorPalette(String id) async {
    _colorPaletteId = id;
    await _dataService.setColorPalette(id);
    notifyListeners();
  }

  Future<void> setBackgroundStyle(String id) async {
    _backgroundStyle = id;
    await _dataService.setBackground(id);
    notifyListeners();
  }

  Future<void> setBackgroundColor(String hex) async {
    _backgroundColorHex = hex;
    await _dataService.setBackgroundColor(hex);
    notifyListeners();
  }

  /// Call this when platform brightness changes (e.g., in WidgetsBindingObserver)
  void updateSystemBrightness(Brightness brightness) {
    if (_themePreference == 'system') {
      _isDarkMode = brightness == Brightness.dark;
      notifyListeners();
    }
  }

  void toggleSound() {
    _isSoundEnabled = !_isSoundEnabled;
    notifyListeners();
  }

  void toggleMusic() {
    _isMusicEnabled = !_isMusicEnabled;
    notifyListeners();
  }

  Future<void> clearBookmarks() async {
    await _dataService.clearBookmarks();
    _bookmarks = await _dataService.getBookmarks();
    notifyListeners();
  }

  List<Question> getRandomQuestions(int count) {
    final shuffled = List<Question>.from(_allQuestions)..shuffle();
    return shuffled.take(count).toList();
  }

  List<Question> getQuestionsByCategory(String category) {
    return _allQuestions.where((q) => q.category == category).toList();
  }

  List<String> get categories {
    return _allQuestions.map((q) => q.category).toSet().toList()..sort();
  }

  Question? getQuestionById(String id) {
    try {
      return _allQuestions.firstWhere((q) => q.id == id);
    } catch (e) {
      return null;
    }
  }

  Future<void> addQuestion(Question question) async {
    await _dataService.addQuestion(question);
    _allQuestions = await _dataService.getQuestions();
    notifyListeners();
  }

  Future<void> addQuestions(List<Question> questions) async {
    await _dataService.addQuestions(questions);
    _allQuestions = await _dataService.getQuestions();
    notifyListeners();
  }

  Future<void> clearAllQuestions() async {
    await _dataService.clearAllQuestions();
    _allQuestions = [];
    notifyListeners();
  }
}
