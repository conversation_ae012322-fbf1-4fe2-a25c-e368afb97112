# Weldable - Welding Study Companion

A comprehensive Flutter mobile application designed as a study companion for welding students and professionals. Weldable provides an interactive, user-friendly way to practice welding theory through multiple choice questions (MCQs) with comprehensive coverage of welding topics.

## Features

### 🏠 Home Screen
- Clean, modern interface with "Weldable" branding
- Quick access to Practice Mode, Mock Test, and Review sections
- Real-time statistics display (questions answered, study streak, bookmarks, tests completed)
- Dark/Light mode toggle
- Beautiful animated gradient background

### 📚 Practice Mode
- **Category-based Practice**: Study questions by specific categories:
  - Electrodes
  - TIG Welding
  - MIG/MAG Welding
  - Metallurgy
  - Inspection
  - Welding Codes
  - Heat Treatment
  - Welding Parameters
  - Materials
  - Safety
  - Welding Defects
  - Welding Positions
- **Instant Feedback**: Get immediate explanations after selecting an answer
- **Random Practice**: Shuffle questions for varied practice sessions
- **Bookmark System**: Save important questions for later review

### ⏱️ Mock Test Mode
- **Configurable Tests**: Choose from 5-25 questions (based on available questions)
- **Timer Options**: 10-45 minute time limits with optional timer
- **Real-time Progress**: Visual progress bar and time remaining display
- **Auto-submit**: Tests automatically submit when time expires
- **Pass/Fail Results**: Clear indication of performance with 70% pass threshold

### 📖 Review Section
- **Wrong Answers**: Review questions answered incorrectly
- **Test History**: View all past test attempts with detailed results
- **Progress Tracking**: Monitor improvement over time
- **Statistics Dashboard**: Comprehensive analytics including:
  - Total tests completed
  - Average score percentage
  - Study streak tracking
  - Total questions attempted

### 🔖 Bookmarks
- Save important questions for quick access
- Practice bookmarked questions in sequence
- Easy bookmark management with bulk operations

### ⚙️ Settings
- Dark/Light theme toggle
- Data management options:
  - Clear bookmarks
  - Clear wrong answers
  - Clear test history
  - Reset statistics
- App information and version details

## Technical Features

### 📱 Cross-Platform
- Runs smoothly on both Android and iOS devices
- Material 3 design principles throughout
- Responsive layout that adapts to different screen sizes

### 💾 Offline-First
- All question data stored locally in JSON format
- No internet connection required for core functionality
- Fast, reliable performance

### 🎨 Modern UI/UX
- Material 3 design system
- Smooth animations and transitions
- Intuitive navigation with bottom tab bar
- Beautiful gradient backgrounds with floating particles

### 📊 Data Management
- Local storage using SharedPreferences
- Comprehensive data models for questions, answers, and test results
- State management with Provider pattern
- Persistent user progress and statistics

## Question Database

The app includes **60+ comprehensive welding questions** covering:

- **Electrodes**: AWS classifications, coating types, applications
- **TIG Welding**: Polarity, shielding gases, electrode types
- **MIG/MAG Welding**: Wire feed, gas mixtures, parameters
- **Metallurgy**: Microstructures, carbon equivalent, phase transformations
- **Inspection**: NDT methods, testing procedures, standards
- **Welding Codes**: AWS, ASME, API standards and requirements
- **Heat Treatment**: Stress relieving, tempering, PWHT
- **Welding Parameters**: Heat input, travel speed, current/voltage
- **Materials**: Steel grades, aluminum alloys, material properties
- **Safety**: PPE requirements, hazard identification, safe practices
- **Welding Defects**: Causes, prevention, identification
- **Welding Positions**: AWS designations, techniques, difficulty levels

Each question includes:
- Multiple choice options (4 choices)
- Detailed explanations
- Difficulty levels (Easy, Medium, Hard)
- Category classification

## Getting Started

### Prerequisites
- Flutter SDK (3.8.1 or higher)
- Dart SDK
- Android Studio / VS Code with Flutter extensions
- Android device/emulator or iOS device/simulator

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd Weldavle
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Run the app**
   ```bash
   flutter run
   ```

### Building for Production

**Android APK:**
```bash
flutter build apk --release
```

**iOS:**
```bash
flutter build ios --release
```

## Project Structure

```
lib/
├── main.dart                 # App entry point
├── models/
│   └── question.dart         # Data models for questions, answers, test results
├── providers/
│   └── app_provider.dart     # State management with Provider
├── screens/
│   ├── home_screen.dart      # Main home screen with navigation
│   ├── practice_screen.dart  # Practice mode with category filtering
│   ├── mock_test_screen.dart # Mock test configuration and execution
│   ├── question_screen.dart  # Individual question display and interaction
│   ├── wrong_answers_screen.dart # Review incorrect answers
│   └── settings_screen.dart  # App settings and data management
├── services/
│   └── data_service.dart     # Local data storage and management
├── theme/
│   └── app_theme.dart        # Material 3 theme configuration
└── widgets/
    ├── animated_background.dart # Animated gradient background
    └── home_menu_button.dart    # Custom menu button component
```

## Dependencies

- **flutter**: SDK
- **provider**: State management
- **shared_preferences**: Local data storage
- **uuid**: Unique ID generation
- **cupertino_icons**: iOS-style icons

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support, questions, or feature requests, please open an issue on the GitHub repository.

---

**Weldable** - Your comprehensive welding study companion for exam preparation and continuous learning! 🔥⚡
