import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_provider.dart';
import '../models/question.dart';
import '../utils/responsive_spacing.dart';
import '../widgets/simple_card.dart';

class StatsScreen extends StatelessWidget {
  const StatsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Statistics')),
      body: Consumer<AppProvider>(
        builder: (context, appProvider, child) {
          final stats = appProvider.userStats;
          final testResults = appProvider.testResults;

          return SingleChildScrollView(
            padding: EdgeInsets.all(RS.horizontalPadding(context)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Overall Stats
                Text(
                  'Overall Performance',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                SizedBox(height: RS.vGapSmall(context)),

                Row(
                  children: [
                    Expanded(
                      child: _buildStatCard(
                        context,
                        'Total Questions',
                        '${stats['totalQuestions'] ?? 0}',
                        Icons.quiz,
                      ),
                    ),
                    SizedBox(width: RS.hGapSmall(context)),
                    Expanded(
                      child: _buildStatCard(
                        context,
                        'Correct Answers',
                        '${stats['correctAnswers'] ?? 0}',
                        Icons.check_circle,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: RS.vGapSmall(context)),

                Row(
                  children: [
                    Expanded(
                      child: _buildStatCard(
                        context,
                        'Average Score',
                        '${(stats['averageScore'] ?? 0.0).toStringAsFixed(1)}%',
                        Icons.trending_up,
                      ),
                    ),
                    SizedBox(width: RS.hGapSmall(context)),
                    Expanded(
                      child: _buildStatCard(
                        context,
                        'Study Streak',
                        '${stats['streak'] ?? 0} days',
                        Icons.local_fire_department,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: RS.vGapSmall(context)),

                Row(
                  children: [
                    Expanded(
                      child: _buildStatCard(
                        context,
                        'Tests Completed',
                        '${stats['testsCompleted'] ?? 0}',
                        Icons.assignment_turned_in,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildStatCard(
                        context,
                        'Bookmarks',
                        '${appProvider.bookmarks.length}',
                        Icons.bookmark,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: RS.vGapMedium(context)),

                // Recent Tests
                Text(
                  'Recent Test Results',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                SizedBox(height: RS.vGapSmall(context)),

                if (testResults.isEmpty) ...[
                  SimpleCard(
                    child: Padding(
                      padding: EdgeInsets.all(RS.cardPadding(context) * 1.6),
                      child: Center(
                        child: Column(
                          children: [
                            Icon(
                              Icons.assessment,
                              size: 48,
                              color: Theme.of(context).colorScheme.outline,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'No test results yet',
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Complete practice sessions or exams to see your results here',
                              style: Theme.of(context).textTheme.bodySmall,
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ] else ...[
                  ...testResults
                      .take(10) // Show last 10 results
                      .map((result) => _buildTestResultCard(context, result)),
                ],

                const SizedBox(height: 20),

                // Category Breakdown
                Text(
                  'Question Categories',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 12),

                ...appProvider.categories.map((category) {
                  final categoryQuestions = appProvider.getQuestionsByCategory(
                    category,
                  );
                  final bookmarkedInCategory = categoryQuestions
                      .where((q) => appProvider.bookmarks.contains(q.id))
                      .length;
                  final wrongInCategory = categoryQuestions
                      .where((q) => appProvider.wrongAnswers.contains(q.id))
                      .length;

                  return SimpleCard(
                    child: ListTile(
                      title: Text(category),
                      subtitle: Text('${categoryQuestions.length} questions'),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          if (bookmarkedInCategory > 0) ...[
                            Icon(
                              Icons.bookmark,
                              size: 16,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                            Text('$bookmarkedInCategory'),
                            const SizedBox(width: 8),
                          ],
                          if (wrongInCategory > 0) ...[
                            Icon(
                              Icons.error_outline,
                              size: 16,
                              color: Theme.of(context).colorScheme.error,
                            ),
                            Text('$wrongInCategory'),
                          ],
                        ],
                      ),
                    ),
                  );
                }).toList(),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, size: 32, color: Theme.of(context).colorScheme.primary),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestResultCard(BuildContext context, TestResult result) {
    final isGoodScore = result.percentage >= 70;

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: isGoodScore ? Colors.green : Colors.orange,
          child: Text(
            '${result.percentage.toInt()}%',
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
        ),
        title: Text(
          result.testType == 'practice' ? 'Practice Session' : 'Exam',
        ),
        subtitle: Text(
          '${result.correctAnswers}/${result.totalQuestions} correct • '
          '${(result.timeSpentSeconds / 60).toStringAsFixed(1)} min',
        ),
        trailing: Text(
          _formatDate(result.completedAt),
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
