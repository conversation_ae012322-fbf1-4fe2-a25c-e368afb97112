// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:testnova/main.dart';

void main() {
  testWidgets('App loads correctly', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const TestnovaApp());

    // Wait for initial frame
    await tester.pump();

    // Wait for loading to complete (with timeout)
    await tester.pump(const Duration(seconds: 2));

    // Verify that the app loads (either loading indicator or content)
    expect(find.byType(MaterialApp), findsOneWidget);
  });
}
