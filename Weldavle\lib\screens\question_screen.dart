import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_provider.dart';
import '../models/question.dart';
import 'package:uuid/uuid.dart';
import 'dart:async';
import '../widgets/animated_background.dart';
import '../utils/responsive_spacing.dart';
import '../widgets/simple_card.dart';

enum QuestionMode { practice, mockTest }

class QuestionScreen extends StatefulWidget {
  final List<Question> questions;
  final int currentIndex;
  final QuestionMode mode;
  final int? timeLimit; // in minutes for exams

  const QuestionScreen({
    super.key,
    required this.questions,
    required this.currentIndex,
    required this.mode,
    this.timeLimit,
  });

  @override
  State<QuestionScreen> createState() => _QuestionScreenState();
}

class _QuestionScreenState extends State<QuestionScreen> {
  late PageController _pageController;
  int _currentIndex = 0;
  final Map<int, int> _selectedAnswers = {};
  bool _showExplanation = false;
  DateTime? _startTime;
  DateTime? _endTime;
  Timer? _timer;
  int _remainingSeconds = 0;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.currentIndex;
    _pageController = PageController(initialPage: _currentIndex);
    _startTime = DateTime.now();

    if (widget.mode == QuestionMode.mockTest && widget.timeLimit != null) {
      _remainingSeconds = widget.timeLimit! * 60;
      _startTimer();
    }
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_remainingSeconds > 0) {
          _remainingSeconds--;
        } else {
          _timer?.cancel();
          _finishSession();
        }
      });
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  // Calculate current score
  Widget _buildCurrentScore() {
    int correctCount = 0;
    int answeredCount = 0;

    _selectedAnswers.forEach((index, selectedAnswer) {
      final question = widget.questions[index];
      if (selectedAnswer == question.correctAnswer) {
        correctCount++;
      }
      answeredCount++;
    });

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Current Score:', style: Theme.of(context).textTheme.titleSmall),
          const SizedBox(height: 4),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.check, color: Colors.green, size: 16),
              const SizedBox(width: 4),
              Text(
                'Correct: $correctCount',
                style: const TextStyle(color: Colors.green),
              ),
              const SizedBox(width: 12),
              Icon(Icons.close, color: Colors.red, size: 16),
              const SizedBox(width: 4),
              Text(
                'Wrong: ${answeredCount - correctCount}',
                style: const TextStyle(color: Colors.red),
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final question = widget.questions[_currentIndex];

    return Consumer<AppProvider>(
      builder: (context, app, _) {
        return BackgroundScaffold(
          backgroundStyle: app.backgroundStyle,
          appBar: AppBar(
            title: Text(
              'Question ${_currentIndex + 1} of ${widget.questions.length}',
            ),
            actions: [
              if (widget.mode == QuestionMode.mockTest &&
                  widget.timeLimit != null)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  margin: const EdgeInsets.only(right: 8),
                  decoration: BoxDecoration(
                    color: _remainingSeconds < 300
                        ? Colors.red
                        : Theme.of(context).colorScheme.primary,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    _formatTime(_remainingSeconds),
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              Consumer<AppProvider>(
                builder: (context, appProvider, child) {
                  final isBookmarked = appProvider.bookmarks.contains(
                    question.id,
                  );
                  return IconButton(
                    icon: Icon(
                      isBookmarked ? Icons.bookmark : Icons.bookmark_border,
                    ),
                    onPressed: () => appProvider.toggleBookmark(question.id),
                  );
                },
              ),
            ],
          ),
          body: Column(
            children: [
              // Progress Bar
              LinearProgressIndicator(
                value: (_currentIndex + 1) / widget.questions.length,
              ),

              Expanded(
                child: PageView.builder(
                  controller: _pageController,
                  itemCount: widget.questions.length,
                  onPageChanged: (index) {
                    setState(() {
                      _currentIndex = index;
                      _showExplanation = false;
                    });
                  },
                  itemBuilder: (context, index) {
                    return _buildQuestionPage(widget.questions[index], index);
                  },
                ),
              ),

              // Navigation and Action Buttons
              _buildBottomActions(),
            ],
          ),
        );
      },
    );
  }

  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  Widget _buildQuestionPage(Question question, int index) {
    final selectedAnswer = _selectedAnswers[index];
    final isAnswered = selectedAnswer != null;
    final showExplanationForThisMode =
        _showExplanation &&
        (widget.mode == QuestionMode.practice ||
            widget.mode == QuestionMode.mockTest);

    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          padding: EdgeInsets.all(RS.horizontalPadding(context)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Question
              SimpleCard(
                child: Padding(
                  padding: EdgeInsets.all(RS.cardPadding(context) * 1.6),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        question.question,
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      SizedBox(height: RS.vGapSmall(context)),
                      Chip(
                        label: Text(question.category),
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(height: RS.vGapMedium(context)),

              // Options
              ...question.options.asMap().entries.map((entry) {
                final optionIndex = entry.key;
                final option = entry.value;
                final isSelected = selectedAnswer == optionIndex;
                final isCorrect = optionIndex == question.correctAnswer;

                Color? cardColor;
                if (isAnswered && showExplanationForThisMode) {
                  if (isCorrect) {
                    cardColor = Colors.green.withValues(alpha: 0.1);
                  } else if (isSelected && !isCorrect) {
                    cardColor = Colors.red.withValues(alpha: 0.1);
                  }
                }

                return Padding(
                  padding: EdgeInsets.only(bottom: RS.vGapSmall(context)),
                  child: SimpleCard(
                    color: cardColor,
                    child: RadioListTile<int>(
                      title: Text(option),
                      value: optionIndex,
                      groupValue: selectedAnswer,
                      onChanged: showExplanationForThisMode
                          ? null
                          : (value) {
                              setState(() {
                                _selectedAnswers[index] = value!;
                              });
                            },
                      activeColor: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                );
              }),

              SizedBox(height: RS.vGapMedium(context)),

              // Explanation
              if (showExplanationForThisMode && isAnswered)
                SimpleCard(
                  color: Theme.of(
                    context,
                  ).colorScheme.primaryContainer.withOpacity(0.28),
                  child: Padding(
                    padding: EdgeInsets.all(RS.cardPadding(context) * 1.2),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              selectedAnswer == question.correctAnswer
                                  ? Icons.check_circle
                                  : Icons.cancel,
                              color: selectedAnswer == question.correctAnswer
                                  ? Colors.green
                                  : Colors.red,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              selectedAnswer == question.correctAnswer
                                  ? 'Correct!'
                                  : 'Incorrect',
                              style: Theme.of(context).textTheme.titleMedium
                                  ?.copyWith(
                                    color:
                                        selectedAnswer == question.correctAnswer
                                        ? Colors.green
                                        : Colors.red,
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                          ],
                        ),
                        SizedBox(height: RS.vGapSmall(context)),
                        // Show current score
                        _buildCurrentScore(),
                        SizedBox(height: RS.vGapSmall(context)),
                        Text(
                          'Explanation:',
                          style: Theme.of(context).textTheme.titleSmall
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 4),
                        Text(question.explanation),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildBottomActions() {
    final selectedAnswer = _selectedAnswers[_currentIndex];
    final isAnswered = selectedAnswer != null;
    final isLastQuestion = _currentIndex == widget.questions.length - 1;

    return Container(
      padding: EdgeInsets.all(RS.cardPadding(context)),
      child: Row(
        children: [
          // Previous Button
          if (_currentIndex > 0)
            Expanded(
              child: OutlinedButton(
                onPressed: () {
                  setState(() {
                    _showExplanation = false;
                  });
                  _pageController.previousPage(
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                  );
                },
                child: const Text('Previous'),
              ),
            ),

          if (_currentIndex > 0) SizedBox(width: RS.hGapSmall(context)),

          // Check Answer / Next Button
          Expanded(
            flex: 2,
            child: ElevatedButton(
              onPressed: isAnswered ? () => _handleNext() : null,
              child: Text(
                !_showExplanation && isAnswered
                    ? 'Check Answer'
                    : isLastQuestion
                    ? 'Finish'
                    : 'Next',
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _handleNext() {
    if (widget.mode == QuestionMode.practice ||
        widget.mode == QuestionMode.mockTest) {
      if (!_showExplanation) {
        setState(() {
          _showExplanation = true;
        });

        // Track wrong answer immediately
        final question = widget.questions[_currentIndex];
        final selectedAnswer = _selectedAnswers[_currentIndex]!;
        if (selectedAnswer != question.correctAnswer) {
          context.read<AppProvider>().addWrongAnswer(question.id);
        }
      } else {
        if (_currentIndex == widget.questions.length - 1) {
          _finishSession();
        } else {
          setState(() {
            _showExplanation = false;
          });
          _pageController.nextPage(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }
      }
    }
  }

  void _finishSession() {
    _timer?.cancel();
    _endTime = DateTime.now();
    final timeSpent = _endTime!.difference(_startTime!).inSeconds;

    // Calculate results
    int correctAnswers = 0;
    List<UserAnswer> userAnswers = [];

    for (int i = 0; i < widget.questions.length; i++) {
      final question = widget.questions[i];
      final selectedAnswer = _selectedAnswers[i];

      if (selectedAnswer != null) {
        final isCorrect = selectedAnswer == question.correctAnswer;
        if (isCorrect) correctAnswers++;

        userAnswers.add(
          UserAnswer(
            questionId: question.id,
            selectedAnswer: selectedAnswer,
            isCorrect: isCorrect,
            timestamp: DateTime.now(),
          ),
        );
      }
    }

    // Save test result
    final testResult = TestResult(
      id: const Uuid().v4(),
      testType: widget.mode == QuestionMode.practice ? 'practice' : 'exam',
      totalQuestions: widget.questions.length,
      correctAnswers: correctAnswers,
      timeSpentSeconds: timeSpent,
      completedAt: _endTime!,
      answers: userAnswers,
    );

    final appProvider = context.read<AppProvider>();
    appProvider.saveTestResult(testResult);

    // After exam completion, add wrong answers to review list
    if (widget.mode == QuestionMode.mockTest) {
      for (int i = 0; i < widget.questions.length; i++) {
        final question = widget.questions[i];
        final selected = _selectedAnswers[i];
        if (selected != null && selected != question.correctAnswer) {
          appProvider.addWrongAnswer(question.id);
        }
      }
    }

    // Navigate back with results
    Navigator.pop(context, testResult);
  }
}
