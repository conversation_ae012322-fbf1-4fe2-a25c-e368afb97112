import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_provider.dart';
import '../models/question.dart';
import 'question_screen.dart';
import '../widgets/animated_background.dart';
import '../utils/responsive_spacing.dart';
import '../widgets/simple_card.dart';

class MockTestScreen extends StatefulWidget {
  const MockTestScreen({super.key});

  @override
  State<MockTestScreen> createState() => _MockTestScreenState();
}

class _MockTestScreenState extends State<MockTestScreen> {
  int _selectedQuestionCount = 10;
  int _selectedTimeLimit = 15; // minutes

  final List<int> _questionCounts = [5, 10, 15, 20, 25];
  final List<int> _timeLimits = [10, 15, 20, 30, 45]; // minutes

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, appProvider, child) {
        final totalQuestions = appProvider.allQuestions.length;

        return BackgroundScaffold(
          appBar: AppBar(title: const Text('Exam')),
          backgroundStyle: appProvider.backgroundStyle,
          body: SingleChildScrollView(
            padding: EdgeInsets.all(RS.horizontalPadding(context)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                SimpleCard(
                  child: Padding(
                    padding: EdgeInsets.all(RS.cardPadding(context) * 1.6),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Exam Setup',
                          style: Theme.of(context).textTheme.headlineSmall,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Configure your exam settings and start practicing under exam conditions.',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: RS.vGapMedium(context)),

                // Question Count Selection
                Text(
                  'Number of Questions',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                SizedBox(height: RS.vGapSmall(context)),
                SimpleCard(
                  child: Padding(
                    padding: EdgeInsets.all(RS.cardPadding(context)),
                    child: Column(
                      children: [
                        Text(
                          'Available: $totalQuestions questions',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                        SizedBox(height: RS.vGapSmall(context)),
                        Wrap(
                          spacing: 8,
                          children: _questionCounts
                              .where((count) => count <= totalQuestions)
                              .map(
                                (count) => ChoiceChip(
                                  label: Text('$count'),
                                  selected: _selectedQuestionCount == count,
                                  onSelected: (selected) {
                                    if (selected) {
                                      setState(
                                        () => _selectedQuestionCount = count,
                                      );
                                    }
                                  },
                                ),
                              )
                              .toList(),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: RS.vGapMedium(context)),

                // Time Limit Selection
                Text(
                  'Time Limit',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                SizedBox(height: RS.vGapSmall(context)),
                SimpleCard(
                  child: Padding(
                    padding: EdgeInsets.all(RS.cardPadding(context)),
                    child: Column(
                      children: [
                        Text(
                          'Recommended: ${(_selectedQuestionCount * 1.5).round()} minutes',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                        SizedBox(height: RS.vGapSmall(context)),
                        Wrap(
                          spacing: 8,
                          children: _timeLimits
                              .map(
                                (time) => ChoiceChip(
                                  label: Text('${time}m'),
                                  selected: _selectedTimeLimit == time,
                                  onSelected: (selected) {
                                    if (selected) {
                                      setState(() => _selectedTimeLimit = time);
                                    }
                                  },
                                ),
                              )
                              .toList(),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: RS.vGapMedium(context)),

                // Test Summary
                SimpleCard(
                  color: Theme.of(
                    context,
                  ).colorScheme.primaryContainer.withOpacity(0.3),
                  child: Padding(
                    padding: EdgeInsets.all(RS.cardPadding(context)),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Test Summary',
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        SizedBox(height: RS.vGapSmall(context)),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text('Questions:'),
                            Text('$_selectedQuestionCount'),
                          ],
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text('Time Limit:'),
                            Text('$_selectedTimeLimit minutes'),
                          ],
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text('Time per Question:'),
                            Text(
                              '${(_selectedTimeLimit * 60 / _selectedQuestionCount).round()} seconds',
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 30),

                // Start Test Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: totalQuestions >= _selectedQuestionCount
                        ? () => _startMockTest(context, appProvider)
                        : null,
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: const Text(
                      'Start Exam',
                      style: TextStyle(fontSize: 18),
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Instructions
                SimpleCard(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Instructions',
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          '\u2022 Answer all questions within the time limit',
                        ),
                        const Text(
                          '\u2022 You can review and change answers before submitting',
                        ),
                        const Text(
                          '\u2022 The test will auto-submit when time expires',
                        ),
                        const Text(
                          '\u2022 Your results will be saved for future reference',
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _startMockTest(BuildContext context, AppProvider appProvider) {
    // Show confirmation dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Start Exam'),
        content: Text(
          'Are you ready to start the exam?\n\n'
          'Questions: $_selectedQuestionCount\n'
          'Time Limit: $_selectedTimeLimit minutes\n\n'
          'The timer will start immediately after you confirm.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _navigateToTest(context, appProvider);
            },
            child: const Text('Start Test'),
          ),
        ],
      ),
    );
  }

  void _navigateToTest(BuildContext context, AppProvider appProvider) {
    final questions = appProvider.getRandomQuestions(_selectedQuestionCount);

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MockTestQuestionScreen(
          questions: questions,
          timeLimit: _selectedTimeLimit,
        ),
      ),
    ).then((result) {
      if (result != null && result is TestResult && context.mounted) {
        _showTestResults(context, result);
      }
    });
  }

  void _showTestResults(BuildContext context, TestResult result) {
    final isPassed = result.percentage >= 70;
    final passColor = isPassed ? Colors.green : Colors.red;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              isPassed ? Icons.check_circle : Icons.cancel,
              color: passColor,
              size: 28,
            ),
            const SizedBox(width: 8),
            const Text('Test Completed!'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Score Card
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: passColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: passColor.withValues(alpha: 0.3)),
              ),
              child: Column(
                children: [
                  Text(
                    '${result.percentage.toStringAsFixed(1)}%',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      color: passColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    '${result.correctAnswers}/${result.totalQuestions} Correct',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // Details
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Time Spent:'),
                Text(
                  '${(result.timeSpentSeconds / 60).toStringAsFixed(1)} minutes',
                ),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Status:'),
                Text(
                  isPassed ? 'PASSED' : 'FAILED',
                  style: TextStyle(
                    color: passColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Message
            Text(
              isPassed
                  ? 'Congratulations! You passed the test! 🎉'
                  : 'Keep practicing to improve your score! 💪',
              style: Theme.of(context).textTheme.titleMedium,
            ),

            if (!isPassed) ...[
              const SizedBox(height: 8),
              Text(
                'Review your mistakes in the "Review Mistakes" section.',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(
                    context,
                  ).colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ],
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}

class MockTestQuestionScreen extends StatelessWidget {
  final List<Question> questions;
  final int timeLimit;

  const MockTestQuestionScreen({
    super.key,
    required this.questions,
    required this.timeLimit,
  });

  @override
  Widget build(BuildContext context) {
    return QuestionScreen(
      questions: questions,
      currentIndex: 0,
      mode: QuestionMode.mockTest,
      timeLimit: timeLimit,
    );
  }
}
