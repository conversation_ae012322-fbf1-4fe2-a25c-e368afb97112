import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_provider.dart';
import '../widgets/animated_background.dart';
import '../widgets/simple_card.dart';
import '../utils/responsive_spacing.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, app, _) {
        final cs = Theme.of(context).colorScheme;
        return BackgroundScaffold(
          appBar: AppBar(title: const Text('Settings')),
          backgroundStyle: app.backgroundStyle,
          body: ListView(
            padding: EdgeInsets.all(RS.horizontalPadding(context)),
            children: [
              _SectionTitle('Appearance'),
              SimpleCard(
                child: SwitchListTile.adaptive(
                  title: const Text('Dark Mode'),
                  subtitle: const Text('Use dark theme throughout the app'),
                  value: app.isDarkMode,
                  onChanged: (_) => app.toggleTheme(),
                ),
              ),
              const SizedBox(height: 16),
              _SectionTitle('Background'),
              SimpleCard(
                child: Padding(
                  padding: EdgeInsets.all(RS.cardPadding(context)),
                  child: LayoutBuilder(
                    builder: (context, constraints) {
                      final isWide = constraints.maxWidth > 600;
                      final items = [
                        {'id': 'pattern', 'name': 'Pattern'},
                        {'id': 'gradient', 'name': 'Gradient'},
                        {'id': 'solid', 'name': 'Solid'},
                      ];

                      return Wrap(
                        spacing: 12,
                        runSpacing: 12,
                        children: items.map((item) {
                          final id = item['id'] as String;
                          final name = item['name'] as String;
                          final selected = app.backgroundStyle == id;

                          return AnimatedContainer(
                            duration: const Duration(milliseconds: 220),
                            width: isWide
                                ? (constraints.maxWidth -
                                          (RS.horizontalPadding(context) * 3)) /
                                      3
                                : (constraints.maxWidth -
                                          (RS.horizontalPadding(context) * 2)) /
                                      2,
                            padding: EdgeInsets.all(RS.cardPadding(context)),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(
                                color: selected
                                    ? Theme.of(context).colorScheme.primary
                                    : Colors.transparent,
                                width: 2,
                              ),
                              color: Theme.of(context).colorScheme.surface,
                            ),
                            child: InkWell(
                              onTap: () => app.setBackgroundStyle(id),
                              borderRadius: BorderRadius.circular(10),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // preview
                                  Container(
                                    height: RS.vGapMedium(context) + 12,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(6),
                                      gradient: id == 'gradient'
                                          ? LinearGradient(
                                              colors: [
                                                Theme.of(
                                                  context,
                                                ).colorScheme.primary,
                                                Theme.of(context)
                                                    .colorScheme
                                                    .secondaryContainer
                                                    .withOpacity(0.9),
                                              ],
                                            )
                                          : null,
                                      color: id == 'solid'
                                          ? Theme.of(context)
                                                .colorScheme
                                                .primary
                                                .withOpacity(0.12)
                                          : null,
                                    ),
                                    child: id == 'pattern'
                                        ? Center(
                                            child: Text(
                                              'WELD',
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .bodyLarge
                                                  ?.copyWith(
                                                    fontWeight: FontWeight.w800,
                                                    color: Theme.of(context)
                                                        .colorScheme
                                                        .onSurfaceVariant,
                                                  ),
                                            ),
                                          )
                                        : const SizedBox.shrink(),
                                  ),
                                  SizedBox(height: RS.vGapSmall(context)),
                                  Text(
                                    name,
                                    style: Theme.of(
                                      context,
                                    ).textTheme.titleMedium,
                                  ),
                                ],
                              ),
                            ),
                          );
                        }).toList(),
                      );
                    },
                  ),
                ),
              ),

              const SizedBox(height: 16),
              _SectionTitle('Color Palette'),
              SimpleCard(
                child: Padding(
                  padding: EdgeInsets.all(RS.cardPadding(context)),
                  child: LayoutBuilder(
                    builder: (context, constraints) {
                      final isWide = constraints.maxWidth > 600;
                      final items = [
                        {
                          'id': 'royal',
                          'name': 'Royal',
                          'colors': [
                            Color(0xFF2E1A8A),
                            Color(0xFF3B82F6),
                            Color(0xFFFFD700),
                          ],
                        },
                        {
                          'id': 'teal',
                          'name': 'Teal',
                          'colors': [
                            Color(0xFF0D9488),
                            Color(0xFF06B6D4),
                            Color(0xFF7DD3FC),
                          ],
                        },
                        {
                          'id': 'golden',
                          'name': 'Golden',
                          'colors': [
                            Color(0xFF8B5E34),
                            Color(0xFFFFD700),
                            Color(0xFFFFA726),
                          ],
                        },
                      ];

                      return Wrap(
                        spacing: 12,
                        runSpacing: 12,
                        children: items.map((item) {
                          final id = item['id'] as String;
                          final colors = item['colors'] as List<Color>;
                          final selected = app.colorPaletteId == id;

                          return AnimatedContainer(
                            duration: const Duration(milliseconds: 250),
                            curve: Curves.easeInOut,
                            width: isWide
                                ? (constraints.maxWidth -
                                          (RS.horizontalPadding(context) * 3)) /
                                      3
                                : (constraints.maxWidth -
                                          (RS.horizontalPadding(context) * 2)) /
                                      2,
                            padding: EdgeInsets.all(RS.cardPadding(context)),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: selected
                                    ? Theme.of(context).colorScheme.primary
                                    : Colors.transparent,
                                width: 2,
                              ),
                              boxShadow: selected
                                  ? [
                                      BoxShadow(
                                        color: Theme.of(
                                          context,
                                        ).colorScheme.primary.withOpacity(0.18),
                                        blurRadius: 12,
                                      ),
                                    ]
                                  : [],
                              color: Theme.of(context).colorScheme.surface,
                            ),
                            child: InkWell(
                              onTap: () => app.setColorPalette(id),
                              borderRadius: BorderRadius.circular(12),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: colors
                                        .map(
                                          (c) => Expanded(
                                            child: Container(
                                              height: RS.vGapSmall(context) * 4,
                                              decoration: BoxDecoration(
                                                color: c,
                                                borderRadius:
                                                    BorderRadius.circular(6),
                                              ),
                                            ),
                                          ),
                                        )
                                        .toList(),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    item['name'] as String,
                                    style: Theme.of(
                                      context,
                                    ).textTheme.titleMedium,
                                  ),
                                ],
                              ),
                            ),
                          );
                        }).toList(),
                      );
                    },
                  ),
                ),
              ),
              const SizedBox(height: 16),
              _SectionTitle('Background Color'),
              SimpleCard(
                child: Padding(
                  padding: EdgeInsets.all(RS.cardPadding(context)),
                  child: Wrap(
                    spacing: RS.hGapMedium(context),
                    runSpacing: RS.vGapMedium(context),
                    children:
                        [
                          '#0F1226',
                          '#1F2937',
                          '#0B3D91',
                          '#0D9488',
                          '#2E1A8A',
                          '#3B82F6',
                        ].map((hex) {
                          final selected = app.backgroundColorHex == hex;
                          Color c;
                          try {
                            c = Color(
                              int.parse('0xFF' + hex.replaceFirst('#', '')),
                            );
                          } catch (_) {
                            c = Theme.of(context).colorScheme.primary;
                          }

                          return GestureDetector(
                            onTap: () => app.setBackgroundColor(hex),
                            child: AnimatedContainer(
                              duration: const Duration(milliseconds: 200),
                              padding: EdgeInsets.all(
                                RS.cardPadding(context) * 0.75,
                              ),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: selected
                                      ? Theme.of(context).colorScheme.primary
                                      : Colors.transparent,
                                  width: 2,
                                ),
                                color: Theme.of(context).colorScheme.surface,
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Container(
                                    width: RS.vGapMedium(context) * 2.5,
                                    height: RS.vGapMedium(context) * 1.8,
                                    decoration: BoxDecoration(
                                      color: c,
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                  ),
                                  SizedBox(width: RS.hGapSmall(context)),
                                  Text(
                                    hex,
                                    style: Theme.of(
                                      context,
                                    ).textTheme.bodyMedium,
                                  ),
                                ],
                              ),
                            ),
                          );
                        }).toList(),
                  ),
                ),
              ),
              _SectionTitle('Data'),
              SimpleCard(
                child: Column(
                  children: [
                    ListTile(
                      leading: const Icon(Icons.bookmark_remove_outlined),
                      title: const Text('Clear Bookmarks'),
                      subtitle: const Text('Remove all bookmarked questions'),
                      onTap: () => _confirm(
                        context,
                        title: 'Clear Bookmarks',
                        message: 'Remove all bookmarked questions?',
                        onConfirm: () async {
                          await app.clearBookmarks();
                          if (context.mounted) {
                            _toast(context, 'Bookmarks cleared');
                          }
                        },
                      ),
                    ),
                    const Divider(height: 0),
                    ListTile(
                      leading: const Icon(Icons.error_outline),
                      title: const Text('Clear Wrong Answers'),
                      subtitle: const Text('Remove all saved wrong answers'),
                      onTap: () => _confirm(
                        context,
                        title: 'Clear Wrong Answers',
                        message: 'Remove all saved wrong answers?',
                        onConfirm: () async {
                          await app.clearWrongAnswers();
                          if (context.mounted) {
                            _toast(context, 'Wrong answers cleared');
                          }
                        },
                      ),
                    ),
                    const Divider(height: 0),
                    ListTile(
                      leading: const Icon(Icons.history),
                      title: const Text('Clear Test History'),
                      subtitle: const Text(
                        'Delete all practice and exam results',
                      ),
                      onTap: () => _confirm(
                        context,
                        title: 'Clear Test History',
                        message: 'Delete all saved test results?',
                        onConfirm: () async {
                          await app.clearTestResults();
                          if (context.mounted) {
                            _toast(context, 'Test history cleared');
                          }
                        },
                      ),
                    ),
                    const Divider(height: 0),
                    ListTile(
                      leading: const Icon(Icons.refresh_outlined),
                      title: const Text('Reset Statistics'),
                      subtitle: const Text(
                        'Reset progress, average score and streak',
                      ),
                      onTap: () => _confirm(
                        context,
                        title: 'Reset Statistics',
                        message: 'Reset your overall statistics?',
                        onConfirm: () async {
                          await app.resetUserStats();
                          if (context.mounted) {
                            _toast(context, 'Statistics reset');
                          }
                        },
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),
              _SectionTitle('About'),
              Card(
                child: ListTile(
                  leading: const Icon(Icons.info_outline),
                  title: const Text('Weldable'),
                  subtitle: const Text(
                    'Modern welding theory and practice tests',
                  ),
                  trailing: Text(
                    'v1.0.0',
                    style: Theme.of(context).textTheme.labelSmall?.copyWith(
                      color: cs.onSurfaceVariant,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _toast(BuildContext context, String message) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(message)));
  }

  void _confirm(
    BuildContext context, {
    required String title,
    required String message,
    required Future<void> Function() onConfirm,
  }) {
    showDialog(
      context: context,
      builder: (_) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () async {
              Navigator.pop(context);
              await onConfirm();
            },
            child: const Text('Confirm'),
          ),
        ],
      ),
    );
  }
}

class _SectionTitle extends StatelessWidget {
  final String text;
  const _SectionTitle(this.text);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        text,
        style: Theme.of(context).textTheme.titleSmall?.copyWith(
          fontWeight: FontWeight.w700,
          color: Theme.of(context).colorScheme.onSurfaceVariant,
        ),
      ),
    );
  }
}
