import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_provider.dart';
import '../models/question.dart';
import 'question_screen.dart';
import '../widgets/animated_background.dart';
import '../utils/responsive_spacing.dart';
import '../widgets/simple_card.dart';

class PracticeScreen extends StatefulWidget {
  const PracticeScreen({super.key});

  @override
  State<PracticeScreen> createState() => _PracticeScreenState();
}

class _PracticeScreenState extends State<PracticeScreen> {
  String? selectedCategory;

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, appProvider, child) {
        final categories = appProvider.categories;
        final questions = selectedCategory == null
            ? appProvider.allQuestions
            : appProvider.getQuestionsByCategory(selectedCategory!);

        return BackgroundScaffold(
          appBar: AppBar(
            title: const Text('Practice Questions'),
            actions: [
              if (selectedCategory != null)
                IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () => setState(() => selectedCategory = null),
                ),
            ],
          ),
          backgroundStyle: appProvider.backgroundStyle,
          body: Column(
            children: [
              // Category Filter
              if (categories.isNotEmpty)
                Container(
                  height: RS.menuButtonHeight(context) * 0.85,
                  padding: EdgeInsets.symmetric(
                    horizontal: RS.horizontalPadding(context),
                  ),
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: categories.length + 1,
                    itemBuilder: (context, index) {
                      if (index == 0) {
                        return Padding(
                          padding: const EdgeInsets.only(right: 8),
                          child: FilterChip(
                            label: const Text('All'),
                            selected: selectedCategory == null,
                            onSelected: (selected) {
                              if (selected) {
                                setState(() => selectedCategory = null);
                              }
                            },
                          ),
                        );
                      }

                      final category = categories[index - 1];
                      return Padding(
                        padding: const EdgeInsets.only(right: 8),
                        child: FilterChip(
                          label: Text(category),
                          selected: selectedCategory == category,
                          onSelected: (selected) {
                            setState(() {
                              selectedCategory = selected ? category : null;
                            });
                          },
                        ),
                      );
                    },
                  ),
                ),

              // Questions List
              Expanded(
                child: questions.isEmpty
                    ? const Center(child: Text('No questions available'))
                    : ListView.builder(
                        padding: EdgeInsets.all(RS.horizontalPadding(context)),
                        itemCount: questions.length,
                        itemBuilder: (context, index) {
                          final question = questions[index];
                          return _buildQuestionCard(
                            context,
                            question,
                            appProvider,
                          );
                        },
                      ),
              ),
            ],
          ),
          floatingActionButton: questions.isNotEmpty
              ? FloatingActionButton.extended(
                  onPressed: () => _startRandomPractice(context, questions),
                  icon: const Icon(Icons.shuffle),
                  label: const Text('Random Practice'),
                  backgroundColor: Theme.of(context).colorScheme.primary,
                )
              : null,
        );
      },
    );
  }

  Widget _buildQuestionCard(
    BuildContext context,
    Question question,
    AppProvider appProvider,
  ) {
    final isBookmarked = appProvider.bookmarks.contains(question.id);
    final isWrongAnswer = appProvider.wrongAnswers.contains(question.id);

    return SimpleCard(
      child: ListTile(
        title: Text(
          question.question,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Row(
              children: [
                Chip(
                  label: Text(
                    question.category,
                    style: const TextStyle(fontSize: 12),
                  ),
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
                const SizedBox(width: 8),
                if (isWrongAnswer)
                  Icon(
                    Icons.error_outline,
                    size: 16,
                    color: Theme.of(context).colorScheme.error,
                  ),
              ],
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: Icon(
                isBookmarked ? Icons.bookmark : Icons.bookmark_border,
                color: isBookmarked
                    ? Theme.of(context).colorScheme.primary
                    : null,
              ),
              onPressed: () => appProvider.toggleBookmark(question.id),
            ),
            const Icon(Icons.arrow_forward_ios),
          ],
        ),
        onTap: () =>
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => QuestionScreen(
                  questions: [question],
                  currentIndex: 0,
                  mode: QuestionMode.practice,
                ),
              ),
            ).then((result) {
              if (result != null && result is TestResult && context.mounted) {
                _showPracticeResults(context, result);
              }
            }),
      ),
    );
  }

  void _startRandomPractice(BuildContext context, List<Question> questions) {
    final shuffledQuestions = List<Question>.from(questions)..shuffle();
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => QuestionScreen(
          questions: shuffledQuestions,
          currentIndex: 0,
          mode: QuestionMode.practice,
        ),
      ),
    ).then((result) {
      if (result != null && result is TestResult && context.mounted) {
        _showPracticeResults(context, result);
      }
    });
  }

  void _showPracticeResults(BuildContext context, TestResult result) {
    final isGoodScore = result.percentage >= 70;
    final scoreColor = isGoodScore ? Colors.green : Colors.orange;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              isGoodScore ? Icons.star : Icons.trending_up,
              color: scoreColor,
              size: 28,
            ),
            const SizedBox(width: 8),
            const Text('Practice Complete!'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Score Card
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: scoreColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: scoreColor.withValues(alpha: 0.3)),
              ),
              child: Column(
                children: [
                  Text(
                    '${result.percentage.toStringAsFixed(1)}%',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      color: scoreColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    '${result.correctAnswers}/${result.totalQuestions} Correct',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // Details
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Time Spent:'),
                Text(
                  '${(result.timeSpentSeconds / 60).toStringAsFixed(1)} minutes',
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Message
            Text(
              isGoodScore
                  ? 'Excellent work! You\'re doing great! 🌟'
                  : 'Good effort! Keep practicing to improve! 💪',
              style: Theme.of(context).textTheme.titleMedium,
            ),

            if (result.correctAnswers < result.totalQuestions) ...[
              const SizedBox(height: 8),
              Text(
                'Review your mistakes in the "Review Mistakes" section.',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(
                    context,
                  ).colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ],
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
