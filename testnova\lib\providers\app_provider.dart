import 'package:flutter/material.dart';
import '../models/question.dart';
import '../services/data_service.dart';

class AppProvider extends ChangeNotifier {
  final DataService _dataService = DataService.instance;

  List<Question> _allQuestions = [];
  Set<String> _bookmarks = {};
  Set<String> _wrongAnswers = {};
  List<TestResult> _testResults = [];
  Map<String, dynamic> _userStats = {};
  bool _isDarkMode = false;
  bool _isLoading = true;

  // Getters
  List<Question> get allQuestions => _allQuestions;
  Set<String> get bookmarks => _bookmarks;
  Set<String> get wrongAnswers => _wrongAnswers;
  List<TestResult> get testResults => _testResults;
  Map<String, dynamic> get userStats => _userStats;
  bool get isDarkMode => _isDarkMode;
  bool get isLoading => _isLoading;

  List<Question> get bookmarkedQuestions =>
      _allQuestions.where((q) => _bookmarks.contains(q.id)).toList();

  List<Question> get wrongAnswerQuestions =>
      _allQuestions.where((q) => _wrongAnswers.contains(q.id)).toList();

  Future<void> initialize() async {
    _isLoading = true;
    notifyListeners();

    try {
      await _dataService.init();
      await _loadData();
    } catch (e) {
      debugPrint('Error initializing app: $e');
    }

    _isLoading = false;
    notifyListeners();
  }

  Future<void> _loadData() async {
    _allQuestions = await _dataService.getQuestions();
    _bookmarks = await _dataService.getBookmarks();
    _wrongAnswers = await _dataService.getWrongAnswers();
    _testResults = await _dataService.getTestResults();
    _userStats = await _dataService.getUserStats();
  }

  Future<void> toggleBookmark(String questionId) async {
    await _dataService.toggleBookmark(questionId);
    _bookmarks = await _dataService.getBookmarks();
    notifyListeners();
  }

  Future<void> addWrongAnswer(String questionId) async {
    await _dataService.addWrongAnswer(questionId);
    _wrongAnswers = await _dataService.getWrongAnswers();
    notifyListeners();
  }

  Future<void> removeWrongAnswer(String questionId) async {
    await _dataService.removeWrongAnswer(questionId);
    _wrongAnswers = await _dataService.getWrongAnswers();
    notifyListeners();
  }

  Future<void> saveTestResult(TestResult result) async {
    await _dataService.saveTestResult(result);
    _testResults = await _dataService.getTestResults();
    await _updateUserStats(result);
    notifyListeners();
  }

  Future<void> _updateUserStats(TestResult result) async {
    final stats = Map<String, dynamic>.from(_userStats);

    stats['totalQuestions'] =
        (stats['totalQuestions'] ?? 0) + result.totalQuestions;
    stats['correctAnswers'] =
        (stats['correctAnswers'] ?? 0) + result.correctAnswers;
    stats['testsCompleted'] = (stats['testsCompleted'] ?? 0) + 1;

    final totalCorrect = stats['correctAnswers'];
    final totalQuestions = stats['totalQuestions'];

    stats['averageScore'] = totalQuestions > 0
        ? (totalCorrect / totalQuestions) * 100
        : 0.0;
    stats['lastStudyDate'] = DateTime.now().toIso8601String();

    // Update streak
    final lastDate = stats['lastStudyDate'];
    if (lastDate != null) {
      final last = DateTime.parse(lastDate);
      final now = DateTime.now();
      final difference = now.difference(last).inDays;

      if (difference <= 1) {
        stats['streak'] = (stats['streak'] ?? 0) + 1;
      } else {
        stats['streak'] = 1;
      }
    } else {
      stats['streak'] = 1;
    }

    _userStats = stats;
    await _dataService.updateUserStats(stats);
  }

  void toggleTheme() {
    _isDarkMode = !_isDarkMode;
    notifyListeners();
  }

  List<Question> getRandomQuestions(int count) {
    final shuffled = List<Question>.from(_allQuestions)..shuffle();
    return shuffled.take(count).toList();
  }

  List<Question> getQuestionsByCategory(String category) {
    return _allQuestions.where((q) => q.category == category).toList();
  }

  List<String> get categories {
    return _allQuestions.map((q) => q.category).toSet().toList()..sort();
  }

  Question? getQuestionById(String id) {
    try {
      return _allQuestions.firstWhere((q) => q.id == id);
    } catch (e) {
      return null;
    }
  }

  Future<void> addQuestion(Question question) async {
    await _dataService.addQuestion(question);
    _allQuestions = await _dataService.getQuestions();
    notifyListeners();
  }

  Future<void> addQuestions(List<Question> questions) async {
    await _dataService.addQuestions(questions);
    _allQuestions = await _dataService.getQuestions();
    notifyListeners();
  }

  Future<void> clearAllQuestions() async {
    await _dataService.clearAllQuestions();
    _allQuestions = [];
    notifyListeners();
  }
}
