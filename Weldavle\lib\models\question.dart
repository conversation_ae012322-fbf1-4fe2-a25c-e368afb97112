class Question {
  final String id;
  final String question;
  final List<String> options;
  final int correctAnswer; // Index of correct answer (0-3)
  final String explanation;
  final String category;
  final String difficulty;

  Question({
    required this.id,
    required this.question,
    required this.options,
    required this.correctAnswer,
    required this.explanation,
    required this.category,
    this.difficulty = 'Medium',
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'question': question,
      'options': options,
      'correctAnswer': correctAnswer,
      'explanation': explanation,
      'category': category,
      'difficulty': difficulty,
    };
  }

  factory Question.fromJson(Map<String, dynamic> json) {
    return Question(
      id: json['id'],
      question: json['question'],
      options: List<String>.from(json['options']),
      correctAnswer: json['correctAnswer'],
      explanation: json['explanation'],
      category: json['category'],
      difficulty: json['difficulty'] ?? 'Medium',
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Question && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

class UserAnswer {
  final String questionId;
  final int selectedAnswer;
  final bool isCorrect;
  final DateTime timestamp;

  UserAnswer({
    required this.questionId,
    required this.selectedAnswer,
    required this.isCorrect,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() {
    return {
      'questionId': questionId,
      'selectedAnswer': selectedAnswer,
      'isCorrect': isCorrect,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory UserAnswer.fromJson(Map<String, dynamic> json) {
    return UserAnswer(
      questionId: json['questionId'],
      selectedAnswer: json['selectedAnswer'],
      isCorrect: json['isCorrect'],
      timestamp: DateTime.parse(json['timestamp']),
    );
  }
}

class TestResult {
  final String id;
  final String testType; // 'practice' or 'exam'
  final int totalQuestions;
  final int correctAnswers;
  final int timeSpentSeconds;
  final DateTime completedAt;
  final List<UserAnswer> answers;

  TestResult({
    required this.id,
    required this.testType,
    required this.totalQuestions,
    required this.correctAnswers,
    required this.timeSpentSeconds,
    required this.completedAt,
    required this.answers,
  });

  double get percentage => (correctAnswers / totalQuestions) * 100;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'testType': testType,
      'totalQuestions': totalQuestions,
      'correctAnswers': correctAnswers,
      'timeSpentSeconds': timeSpentSeconds,
      'completedAt': completedAt.toIso8601String(),
      'answers': answers.map((a) => a.toJson()).toList(),
    };
  }

  factory TestResult.fromJson(Map<String, dynamic> json) {
    return TestResult(
      id: json['id'],
      testType: json['testType'],
      totalQuestions: json['totalQuestions'],
      correctAnswers: json['correctAnswers'],
      timeSpentSeconds: json['timeSpentSeconds'],
      completedAt: DateTime.parse(json['completedAt']),
      answers: (json['answers'] as List)
          .map((a) => UserAnswer.fromJson(a))
          .toList(),
    );
  }
}
