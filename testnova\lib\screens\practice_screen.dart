import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_provider.dart';
import '../models/question.dart';
import 'question_screen.dart';

class PracticeScreen extends StatefulWidget {
  const PracticeScreen({super.key});

  @override
  State<PracticeScreen> createState() => _PracticeScreenState();
}

class _PracticeScreenState extends State<PracticeScreen> {
  String? selectedCategory;

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, appProvider, child) {
        final categories = appProvider.categories;
        final questions = selectedCategory == null
            ? appProvider.allQuestions
            : appProvider.getQuestionsByCategory(selectedCategory!);

        return Scaffold(
          appBar: AppBar(
            title: const Text('Practice Questions'),
            actions: [
              if (selectedCategory != null)
                IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () => setState(() => selectedCategory = null),
                ),
            ],
          ),
          body: Column(
            children: [
              // Category Filter
              if (categories.isNotEmpty)
                Container(
                  height: 60,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: categories.length + 1,
                    itemBuilder: (context, index) {
                      if (index == 0) {
                        return Padding(
                          padding: const EdgeInsets.only(right: 8),
                          child: FilterChip(
                            label: const Text('All'),
                            selected: selectedCategory == null,
                            onSelected: (selected) {
                              if (selected) {
                                setState(() => selectedCategory = null);
                              }
                            },
                          ),
                        );
                      }
                      
                      final category = categories[index - 1];
                      return Padding(
                        padding: const EdgeInsets.only(right: 8),
                        child: FilterChip(
                          label: Text(category),
                          selected: selectedCategory == category,
                          onSelected: (selected) {
                            setState(() {
                              selectedCategory = selected ? category : null;
                            });
                          },
                        ),
                      );
                    },
                  ),
                ),

              // Questions List
              Expanded(
                child: questions.isEmpty
                    ? const Center(
                        child: Text('No questions available'),
                      )
                    : ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: questions.length,
                        itemBuilder: (context, index) {
                          final question = questions[index];
                          return _buildQuestionCard(context, question, appProvider);
                        },
                      ),
              ),
            ],
          ),
          floatingActionButton: questions.isNotEmpty
              ? FloatingActionButton.extended(
                  onPressed: () => _startRandomPractice(context, questions),
                  icon: const Icon(Icons.shuffle),
                  label: const Text('Random Practice'),
                )
              : null,
        );
      },
    );
  }

  Widget _buildQuestionCard(BuildContext context, Question question, AppProvider appProvider) {
    final isBookmarked = appProvider.bookmarks.contains(question.id);
    final isWrongAnswer = appProvider.wrongAnswers.contains(question.id);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        title: Text(
          question.question,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Row(
              children: [
                Chip(
                  label: Text(
                    question.category,
                    style: const TextStyle(fontSize: 12),
                  ),
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
                const SizedBox(width: 8),
                if (isWrongAnswer)
                  Icon(
                    Icons.error_outline,
                    size: 16,
                    color: Theme.of(context).colorScheme.error,
                  ),
              ],
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: Icon(
                isBookmarked ? Icons.bookmark : Icons.bookmark_border,
                color: isBookmarked ? Theme.of(context).colorScheme.primary : null,
              ),
              onPressed: () => appProvider.toggleBookmark(question.id),
            ),
            const Icon(Icons.arrow_forward_ios),
          ],
        ),
        onTap: () => Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => QuestionScreen(
              questions: [question],
              currentIndex: 0,
              mode: QuestionMode.practice,
            ),
          ),
        ),
      ),
    );
  }

  void _startRandomPractice(BuildContext context, List<Question> questions) {
    final shuffledQuestions = List<Question>.from(questions)..shuffle();
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => QuestionScreen(
          questions: shuffledQuestions,
          currentIndex: 0,
          mode: QuestionMode.practice,
        ),
      ),
    );
  }
}
