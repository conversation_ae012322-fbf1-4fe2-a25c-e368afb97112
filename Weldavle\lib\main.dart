import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'providers/app_provider.dart';
import 'screens/home_screen.dart';
import 'services/data_service.dart';
import 'theme/app_theme.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  // Initialize local storage and seed default questions for offline use
  await DataService.instance.init();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => AppProvider()..initialize(),
      child: Consumer<AppProvider>(
        builder: (context, app, _) {
          final theme = AppTheme.themeForPalette(
            app.colorPaletteId,
            Brightness.light,
          );
          final darkTheme = AppTheme.themeForPalette(
            app.colorPaletteId,
            Brightness.dark,
          );

          return MaterialApp(
            title: 'Weldable',
            theme: theme,
            darkTheme: darkTheme,
            // Wrap the app with a BrightnessObserver which will inform
            // AppProvider when system brightness changes so 'system' theme
            // follows the platform preference. Backgrounds are rendered by
            // each screen's `BackgroundScaffold` so we avoid a constant
            // top-level background which would not rebuild on theme change.
            builder: (context, child) =>
                BrightnessObserver(child: child ?? const SizedBox.shrink()),
            themeMode: app.isDarkMode ? ThemeMode.dark : ThemeMode.light,
            home: const HomeScreen(),
          );
        },
      ),
    );
  }
}

class BrightnessObserver extends StatefulWidget {
  final Widget child;
  const BrightnessObserver({required this.child, super.key});

  @override
  State<BrightnessObserver> createState() => _BrightnessObserverState();
}

class _BrightnessObserverState extends State<BrightnessObserver>
    with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    // initial notify after first frame to avoid notifying listeners during build
    WidgetsBinding.instance.addPostFrameCallback((_) => _notifyBrightness());
  }

  void _notifyBrightness() {
    final brightness = WidgetsBinding.instance.window.platformBrightness;
    final provider = Provider.of<AppProvider>(context, listen: false);
    provider.updateSystemBrightness(brightness);
  }

  @override
  void didChangePlatformBrightness() {
    _notifyBrightness();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) => widget.child;
}
