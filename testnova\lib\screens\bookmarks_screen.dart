import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_provider.dart';
import '../models/question.dart';
import 'question_screen.dart';

class BookmarksScreen extends StatelessWidget {
  const BookmarksScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Bookmarked Questions'),
      ),
      body: Consumer<AppProvider>(
        builder: (context, appProvider, child) {
          final bookmarkedQuestions = appProvider.bookmarkedQuestions;

          if (bookmarkedQuestions.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.bookmark_border,
                    size: 64,
                    color: Theme.of(context).colorScheme.outline,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No bookmarked questions',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Bookmark questions while practicing to save them here',
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              // Header with count and actions
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        '${bookmarkedQuestions.length} bookmarked questions',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                    ),
                    ElevatedButton.icon(
                      onPressed: () => _startBookmarkedPractice(context, bookmarkedQuestions),
                      icon: const Icon(Icons.play_arrow),
                      label: const Text('Practice All'),
                    ),
                  ],
                ),
              ),

              // Questions List
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: bookmarkedQuestions.length,
                  itemBuilder: (context, index) {
                    final question = bookmarkedQuestions[index];
                    return _buildQuestionCard(context, question, appProvider);
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildQuestionCard(BuildContext context, Question question, AppProvider appProvider) {
    final isWrongAnswer = appProvider.wrongAnswers.contains(question.id);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        title: Text(
          question.question,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Row(
              children: [
                Chip(
                  label: Text(
                    question.category,
                    style: const TextStyle(fontSize: 12),
                  ),
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
                const SizedBox(width: 8),
                if (isWrongAnswer)
                  Icon(
                    Icons.error_outline,
                    size: 16,
                    color: Theme.of(context).colorScheme.error,
                  ),
              ],
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.bookmark),
              color: Theme.of(context).colorScheme.primary,
              onPressed: () => appProvider.toggleBookmark(question.id),
            ),
            const Icon(Icons.arrow_forward_ios),
          ],
        ),
        onTap: () => Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => QuestionScreen(
              questions: [question],
              currentIndex: 0,
              mode: QuestionMode.practice,
            ),
          ),
        ),
      ),
    );
  }

  void _startBookmarkedPractice(BuildContext context, List<Question> questions) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => QuestionScreen(
          questions: questions,
          currentIndex: 0,
          mode: QuestionMode.practice,
        ),
      ),
    );
  }
}
