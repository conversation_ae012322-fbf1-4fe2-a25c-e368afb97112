import 'package:flutter/material.dart';
import 'dart:math';

/// Widget to create an animated repeating text pattern background
class AnimatedWeldableBackground extends StatefulWidget {
  final String text;
  // Responsive repeating background text. The widget computes an
  // appropriate number of rows/columns and text size based on available
  // space. The text color and opacity adapt to the current Theme brightness.
  final TextStyle? style;
  final String backgroundStyle; // 'pattern'|'gradient'|'solid'

  const AnimatedWeldableBackground({
    super.key,
    this.text = 'Weldable',
    this.style,
    this.backgroundStyle = 'pattern',
  });

  @override
  State<AnimatedWeldableBackground> createState() =>
      _AnimatedWeldableBackgroundState();
}

class _AnimatedWeldableBackgroundState extends State<AnimatedWeldableBackground>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  // no per-row random seeds required now; animation uses sin pulses

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 60),
    );

    _controller.repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Determine theme-aware color + opacity
        final brightness = Theme.of(context).brightness;
        final bool isDark = brightness == Brightness.dark;

        // Choose a base font size relative to width
        final double baseFontSize = (constraints.maxWidth / 28).clamp(
          12.0,
          46.0,
        );

        // Compute rows & columns to fill the area without overcrowding
        final int rows = (constraints.maxHeight / (baseFontSize * 1.8))
            .clamp(4, 48)
            .floor();
        final int columns = (constraints.maxWidth / (baseFontSize * 6))
            .clamp(3, 30)
            .floor();

        final color = isDark
            ? Colors.white.withOpacity(0.04)
            : Colors.black.withOpacity(0.06);

        final effectiveStyle = (widget.style ?? const TextStyle()).copyWith(
          fontSize: baseFontSize,
          fontWeight: FontWeight.w700,
          color: color,
        );

        return Column(
          children: List.generate(rows, (row) {
            // if we're rendering gradient/solid, we don't need per-row text
            if (widget.backgroundStyle != 'pattern') {
              return const SizedBox.shrink();
            }

            return AnimatedBuilder(
              animation: _controller,
              builder: (context, child) {
                // subtle horizontal oscillation per row
                final dx =
                    sin((_controller.value * 2 * pi) + row) *
                    (12 + (row % 3) * 6);
                return Transform.translate(
                  offset: Offset(dx, 0),
                  child: Row(
                    children: List.generate(columns, (col) {
                      return SizedBox(
                        width: constraints.maxWidth / columns,
                        height: constraints.maxHeight / rows,
                        child: Center(
                          child: Transform.rotate(
                            angle: (row % 2 == 0) ? 0 : 0.02,
                            child: Text(
                              widget.text,
                              style: effectiveStyle,
                              overflow: TextOverflow.visible,
                            ),
                          ),
                        ),
                      );
                    }),
                  ),
                );
              },
            );
          }),
        );
      },
    );
  }
}

/// Widget to add the animated background to any screen
class BackgroundScaffold extends StatelessWidget {
  final Widget body;
  final PreferredSizeWidget? appBar;
  final Widget? bottomNavigationBar;
  final Color backgroundColor;
  final String? backgroundColorHex;
  final String backgroundText;
  final String backgroundStyle;
  final Widget? floatingActionButton;
  final FloatingActionButtonLocation? floatingActionButtonLocation;
  final Widget? drawer;
  final Widget? endDrawer;
  final bool? resizeToAvoidBottomInset;

  const BackgroundScaffold({
    super.key,
    required this.body,
    this.appBar,
    this.bottomNavigationBar,
    this.backgroundColor = const Color(0xFF0F1226), // Royal indigo background
    this.backgroundColorHex,
    this.backgroundText = 'WELDABLE',
    this.backgroundStyle = 'pattern',
    this.floatingActionButton,
    this.floatingActionButtonLocation,
    this.drawer,
    this.endDrawer,
    this.resizeToAvoidBottomInset,
  });

  @override
  Widget build(BuildContext context) {
    // determine effective background color from provided hex if any
    Color effectiveBg = backgroundColor;
    if (backgroundColorHex != null) {
      try {
        final hex = backgroundColorHex!.replaceFirst('#', '');
        if (hex.length == 6) {
          effectiveBg = Color(int.parse('0xFF$hex'));
        } else if (hex.length == 8) {
          effectiveBg = Color(int.parse('0x$hex'));
        }
      } catch (_) {
        effectiveBg = backgroundColor;
      }
    }

    return Scaffold(
      backgroundColor: effectiveBg,
      appBar: appBar,
      // Ensure any provided bottomNavigationBar is rendered transparently
      // so the animated/background visuals are visible behind it.
      bottomNavigationBar: bottomNavigationBar == null
          ? null
          : Theme(
              data: Theme.of(context).copyWith(
                bottomNavigationBarTheme: Theme.of(context)
                    .bottomNavigationBarTheme
                    .copyWith(
                      backgroundColor: Colors.transparent,
                      elevation: 0,
                    ),
              ),
              child: bottomNavigationBar!,
            ),
      floatingActionButton: floatingActionButton,
      floatingActionButtonLocation: floatingActionButtonLocation,
      drawer: drawer,
      endDrawer: endDrawer,
      resizeToAvoidBottomInset: resizeToAvoidBottomInset,
      body: Stack(
        children: [
          // Animated background pattern
          Positioned.fill(child: _buildBackground(context)),
          // Main content
          body,
        ],
      ),
    );
  }

  Widget _buildBackground(BuildContext context) {
    // compute effective background color from hex if provided
    Color effectiveBg = backgroundColor;
    if (backgroundColorHex != null) {
      try {
        final hex = backgroundColorHex!.replaceFirst('#', '');
        if (hex.length == 6) {
          effectiveBg = Color(int.parse('0xFF$hex'));
        } else if (hex.length == 8) {
          effectiveBg = Color(int.parse('0x$hex'));
        }
      } catch (_) {
        effectiveBg = backgroundColor;
      }
    }

    if (backgroundStyle == 'gradient') {
      // animated subtle gradient
      final primary = Theme.of(context).colorScheme.primary;
      Color base = primary;
      if (backgroundColorHex != null) {
        try {
          final hex = backgroundColorHex!.replaceFirst('#', '');
          if (hex.length == 6) {
            base = Color(int.parse('0xFF$hex'));
          } else if (hex.length == 8) {
            base = Color(int.parse('0x$hex'));
          }
        } catch (_) {
          base = primary;
        }
      }

      return AnimatedGradientBackground(
        colors: [base, Theme.of(context).colorScheme.secondaryContainer],
      );
    }

    if (backgroundStyle == 'solid') {
      return Container(color: effectiveBg.withOpacity(1.0));
    }

    return AnimatedWeldableBackground(
      text: backgroundText,
      style: const TextStyle(fontSize: 32, fontWeight: FontWeight.bold),
      backgroundStyle: 'pattern',
    );
  }
}

/// Animated gradient helper
class AnimatedGradientBackground extends StatefulWidget {
  final List<Color> colors;
  // interpolation amplitude for color lerp (0..1)
  final double amplitude;
  const AnimatedGradientBackground({
    super.key,
    required this.colors,
    this.amplitude = 0.08,
  });

  @override
  State<AnimatedGradientBackground> createState() =>
      _AnimatedGradientBackgroundState();
}

class _AnimatedGradientBackgroundState extends State<AnimatedGradientBackground>
    with SingleTickerProviderStateMixin {
  late AnimationController _ctrl;

  @override
  void initState() {
    super.initState();
    _ctrl = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 10),
    )..repeat(reverse: true);
  }

  @override
  void dispose() {
    _ctrl.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _ctrl,
      builder: (context, child) {
        final t = _ctrl.value;
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: widget.colors
                  .map((c) => Color.lerp(c, Colors.black, 0.08 * t)!)
                  .toList(),
            ),
          ),
        );
      },
    );
  }
}
